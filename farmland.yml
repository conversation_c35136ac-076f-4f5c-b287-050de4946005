Farmland:
  - ModuleName: "BKM"
    Owner: "陈伟"
    FilePath:
      - "ftl/bkm/ftl_bkm_remap.c"
      - "ftl/bkm/ftl_bkm.c"
      - "ftl/bkm/ftl_bkm.h"
      - "ftl/include/ftl_bkm_api.h"
      - "ftl/bkm/ftl_bkm_policy.c"
      - "ftl/bkm/ftl_bkm_policy.h"  
      - "ftl/bkm/ftl_bkm_init.c"
  - ModuleName: "BKM"
    Owner: "洪灵杰"
    FilePath:
      - "ftl/bkm/ftl_bkm_res.c"
  
  - ModuleName: "BM"
    Owner: "洪灵杰"
    FilePath:
      - "ftl/bm"
      - "ftl/include/ftl_bm_api.h"

  - ModuleName: "SPOR"
    Owner: "洪灵杰"
    FilePath:
      - "ftl/spor/ftl_spor.c"
      - "ftl/spor/ftl_spor.h"
      - "ftl/include/ftl_spor_api.h"  
  - ModuleName: "CTL"
    Owner: "潘晓彬"
    FilePath:
      - "ftl/ctl"
      - "ftl/ctl/include/ftl_ctl_api.h"
      - "ftl/include/ftl_ctl_api.h"

  - ModuleName: "GC"
    Owner: "邵宇杰"
    FilePath:
      - "ftl/gc"
      - "ftl/include/ftl_gc_api.h"
  - ModuleName: "WB"
    Owner: "邵宇杰"
    FilePath:
      - "ftl/wb"
      - "ftl/include/ftl_wb_api.h"


  - ModuleName: "GI"
    Owner: "洪灵杰"
    FilePath:
      - "ftl/gi"
      - "ftl/include/ftl_gi_api.h"
  - ModuleName: "LIB"
    Owner: "肖翔"
    FilePath:
      - "ftl/lib"
      - "ftl/include/ftl_assert.h"
      - "ftl/include/ftl_base_api.h"
      - "ftl/include/ftl_config_api.h"
      - "ftl/include/ftl_func_register_api.h"
      - "ftl/include/ftl_mem_map.h"
      - "ftl/include/ftl_queue.h"
      - "ftl/include/ftl_schedule_api.h"
  - ModuleName: "MAPPING"
    Owner: "游世龙"
    FilePath:
      - "ftl/mapping/ftl_mapping.c"
      - "ftl/mapping/ftl_mapping.h"
      - "ftl/include/ftl_mapping_api.h"
  - ModuleName: "MAPPING"
    Owner: "李腾"
    FilePath:
      - "ftl/mapping/ftl_tabblk_mgr.c"
      - "ftl/mapping/ftl_tabblk_mgr.h"
  - ModuleName: "PFE"
    Owner: "游世龙"
    FilePath:
      - "ftl/pfe"
      - "ftl/include/ftl_pfe_api.h"
  - ModuleName: "RB"
    Owner: "陈伟"
    FilePath:
      - "ftl/rb"
      - "ftl/include/ftl_rb_api.h"
  - ModuleName: "READ"
    Owner: "倪礼彬"
    FilePath:
      - "ftl/read"
      - "ftl/include/ftl_read_api.h"
  - ModuleName: "WRITE"
    Owner: "马俊凯"
    FilePath:
      - "ftl/write"
      - "ftl/include/ftl_write_api.h"
  - ModuleName: "IF"
    Owner: "肖翔"
    FilePath:
      - "ftl/ftl_if.c"
      - "ftl/ftl_if.h"

