#!/bin/bash

# 统一环境配置
export LANG=C
export LC_ALL=C

# 仅保留Linux环境支持
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS_TYPE="linux"
    BUILD_DIR="./build-linux"
    PRESET="linux32"  # 修改预设为linux，移除32位标识
else
    echo "Unsupported OS: $OSTYPE"
    exit 1
fi

# 参数检查
if [ $# -eq 0 ]; then
    echo "Usage: $0 <module_name>"
    echo "Available modules: gc, write, mapping, bkm, bm, read, gi, ctl, rb, lib, all"
    exit 1
fi
MODULE=$1

TEST_TARGET="run_${MODULE}_test"

# 确保构建目录存在
mkdir -p "$BUILD_DIR"

# 执行构建（添加Linux编译工具路径）
echo "Configuring CMake with preset: $PRESET"
cmake --preset "$PRESET" -DFTL_TARGET="$MODULE" -DOSTYPE="$OS_TYPE" \
      -DCMAKE_C_COMPILER=$(which gcc) \
      -DCMAKE_CXX_COMPILER=$(which g++)

if [ $? -ne 0 ]; then
    echo "CMake配置失败: $MODULE"
    exit 1
fi

echo "构建模块: $MODULE"
cmake --build "$BUILD_DIR" --target "$TEST_TARGET" -- -j$(nproc)

if [ $? -ne 0 ]; then
    echo "构建失败: $MODULE"
    exit 1
fi

echo "模块 $MODULE 测试完成"