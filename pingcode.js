// PingCode缺陷统计与可视化页面逻辑

// 全局常量定义
// 字典映射
const DICT = {
  severity: {
    "5cb7e6e2fda1ce4ca0020004": "致命",
    "5cb7e6e2fda1ce4ca0020003": "严重",
    "5cb7e6e2fda1ce4ca0020002": "一般",
    "5cb7e6e2fda1ce4ca0020001": "提示"
  },
  priority: {
    "5cb9466afda1ce4ca0090005": "最高",
    "5cb9466afda1ce4ca0090004": "较高",
    "5cb9466afda1ce4ca0090003": "普通",
    "5cb9466afda1ce4ca0090002": "较低",
    "5cb9466afda1ce4ca0090001": "最低"
  },
  wentileibie: {
    "6784e5f775c65f70f13d048f": "功能问题",
    "6784e5f775c65f70f13d0490": "性能问题",
    "6784e5f775c65f70f13d0492": "兼容性问题",
    "6786336b75c65f70f13d4f51": "稳定性问题",
    "6786336b75c65f70f13d4f52": "可靠性/认证问题",
    "6784e5f775c65f70f13d0493": "硬件问题",
    "6784e5f775c65f70f13d0494": "结构问题",
    "6784e5f775c65f70f13d0495": "散热问题",
    "6784e5f775c65f70f13d0498": "来料问题",
    "685e31ca4c1dd020bbe84ca4": "NAND/DDR问题",
    "6784e5f775c65f70f13d0496": "工艺问题",
    "6784e5f775c65f70f13d0497": "生产测试问题",
    "6784e5f775c65f70f13d0499": "安全问题",
    "6784e5f775c65f70f13d049c": "芯片问题",
    "6788c11275c65f70f13d763f": "环境问题",
    "6784f68575c65f70f13d0c34": "功能问题"
  },
  shifouyonglifaxian: {
    "673599ff2af553ee7ba6ad8f": "用例发现",
    "673599ff2af553ee7ba6ad90": "非用例发现"
  },
  shifoukaifaziti: {
    "67a72ac04498bcd87224cbc0": "固件",
    "67a72ac04498bcd87224cbc1": "硬件",
    "67b45951eca31ee2556321a5": "测试",
    "67b45951eca31ee2556321a6": "产品工程",
    "67b45951eca31ee2556321a7": "FAE(线上)",
    "685e33e44c1dd020bbe84d86": "FAE(OEM)",
    "67b45951eca31ee2556321a8": "质量"
  },
  xiangmujieduan: {
    "67ad59baeca31ee2556286d1": "TR1/2/3  Pre EVT",
    "67ad59baeca31ee2556286d2": "TR4/TR4A EVT",
    "67ad59baeca31ee2556286d3": "TR5 DVT",
    "67ad59baeca31ee2556286d4": "TR6 PVT"
  },
  kaifamokuai: {
    "6763cd740323b6428bd79491": "硬件",
    "6784e75675c65f70f13d04fe": "固件",
    "6784e75675c65f70f13d04ff": "生产",
    "6784e75675c65f70f13d0500": "来料",
    "6789fbf375c65f70f13d8b2b": "其他"
  },
  reappear_probability: {
    "5cd9350fe32f8d44d0060004": "必现",
    "5cd9350fe32f8d44d0060003": "大概率复现",
    "5cd9350fe32f8d44d0060002": "小概率复现",
    "5cd9350fe32f8d44d0060001": "仅出现一次"
  },
  reason: {
    "5cb7e7dafda1ce4ca0030001": "基本场景侧漏",
    "5cb7e7dafda1ce4ca0030002": "特殊场景侧漏",
    "5cb7e7dafda1ce4ca0030003": "重复问题",
    "5cb7e7dafda1ce4ca0030004": "新需求引入",
    "5cb7e7dafda1ce4ca0030005": "修改引入",
    "5cb7e7dafda1ce4ca0030006": "接口变更",
    "5cb7e7dafda1ce4ca0030007": "非问题",
    "675aadc10323b6428bd6d391": "重新打开",
    "6784c6d075c65f70f13cfcfe": "问题降级"
  },
  solution: {
    "5ce63fd412c2829090080001": "按设计实现",
    "5ce63fd412c2829090080002": "无法重现",
    "5ce63fd412c2829090080003": "转需求",
    "5ce63fd412c2829090080004": "重复",
    "5ce63fd412c2829090080005": "已修复",
    "5ce63fd412c2829090080006": "已修复并验证",
    "5ce63fd412c2829090080007": "暂不修复",
    "5ce63fd412c2829090080008": "已过时",
    "5ce63fd412c2829090080009": "误报"
  },
  huiguiceshijielun: {
    "67359a7a2af553ee7ba6ada4": "通过",
    "67359a7a2af553ee7ba6ada5": "不通过"
  },
  guanbileixing: {
    "67359a432af553ee7ba6ad94": "问题解决关闭",
    "67359a432af553ee7ba6ad95": "非问题关闭",
    "67359a432af553ee7ba6ad96": "重复问题关闭",
    "67e3c6ae30719d92565beb5f": "不解决关闭"
  }
};

// UFS模块字典（用于新增的统计图表）
const UFS_MODULES = {
  "686735ad4afc5f95cd32db56": "BKM",
  "686735ad4afc5f95cd32db57": "BM", 
  "68676c274afc5f95cd32e503": "CTL",
  "68676c274afc5f95cd32e504": "GC",
  "68676c274afc5f95cd32e505": "GI",
  "68676c274afc5f95cd32e506": "LIB",
  "68676c274afc5f95cd32e507": "MAPPING",
  "68676c274afc5f95cd32e508": "PFE",
  "68676c274afc5f95cd32e509": "RB",
  "68676c274afc5f95cd32e50a": "READ",
  "68676c274afc5f95cd32e50b": "SPOR",
  "68676c274afc5f95cd32e50c": "WRITE",
  "68676c274afc5f95cd32e50d": "LP",
  "68676c274afc5f95cd32e50e": "NFL",
  "68676c274afc5f95cd32e50f": "PLAT",
  "68676c274afc5f95cd32e510": "PTC",
  "68676c274afc5f95cd32e511": "RDT",
  "68676c274afc5f95cd32e512": "TOP",
  "68676c274afc5f95cd32e513": "FE",
  "68676c274afc5f95cd32e514": "HOST",
  "68676c274afc5f95cd32e515": "BUILD",
  "68676c274afc5f95cd32e516": "STATIC_CHECK",
  "68676c274afc5f95cd32e517": "其他"
};

// 页面切换逻辑（仅缺陷统计相关）
function showPingCodePage(page) {
  // 隐藏所有问题单子页面
  const defectPages = [
    'defect-submit-count',
    'defect-overdue', 
    'defect-location-overdue',
    'defect-di',
    'defect-trend',
    'defect-avg-time',
    'defect-module',
    'defect-submit-compare',
    'defect-resolution-rate'
  ];
  
  defectPages.forEach(pageId => {
    document.getElementById(pageId).classList.add('hidden');
  });
  
  // 隐藏所有需求相关页面
  const demandPages = [
    'pingcode-demand',
    'pingcode-demand-list',
    'pingcode-demand-chart',
    'pingcode-iteration-distribution',
    'pingcode-iteration-progress',
    'pingcode-person-progress'
  ];
  
  demandPages.forEach(pageId => {
    const element = document.getElementById(pageId);
    if (element) {
      element.classList.add('hidden');
    }
  });
  
  // 显示选中的页面
  const targetElement = document.getElementById(page);
  if (targetElement) {
    targetElement.classList.remove('hidden');
  }
  
  // 根据页面类型执行相应的渲染逻辑
  switch(page) {
    case 'pingcode-demand':
      renderPingCodeTable('demand');
      fetchPingCodeData('demand', 1);
      break;
    case 'pingcode-demand-list':
      renderPingCodeTable('demand');
      fetchPingCodeData('demand', 1);
      break;
    case 'pingcode-demand-chart':
      // 这里可能需要调用相应的图表渲染函数
      break;
    case 'pingcode-iteration-distribution':
      // 这里可能需要调用相应的图表渲染函数
      break;
    case 'pingcode-iteration-progress':
      // 这里可能需要调用相应的图表渲染函数
      break;
    case 'pingcode-person-progress':
      // 调用各负责人需求进展图表渲染函数
      if (typeof loadPingCodePersonProgressChart === 'function') {
        loadPingCodePersonProgressChart();
      }
      break;
  }
}

// 问题单主菜单点击处理（不切换页面，只展开子菜单）
function handleDefectMainMenuClick() {
  // 不执行任何页面切换，只展开/收起子菜单
  // 页面切换由子菜单项处理
}

// 问题单子页面切换逻辑
function showDefectPage(page) {
  // 隐藏所有问题单子页面和其他页面
  const defectPages = [

    'defect-list',
    'defect-submit-count',
    'defect-overdue', 
    'defect-location-overdue',
    'defect-di',
    'defect-trend',
    'defect-avg-time',
    'defect-module',
    'defect-submit-compare',
    'defect-resolution-rate'
  ];
  
  // 隐藏所有问题单子页面
  defectPages.forEach(pageId => {
    document.getElementById(pageId).classList.add('hidden');
  });
  
  // 隐藏其他页面
  document.getElementById('pingcode-demand').classList.add('hidden');
  
  // 显示选中的页面
  document.getElementById(page).classList.remove('hidden');
  
  // 根据页面类型渲染对应的图表
  switch(page) {
    case 'defect-dashboard':
      setTimeout(renderDefectDashboard, 0);
      break;
    case 'defect-list':
      setTimeout(renderDefectListPage, 0);
      break;
    case 'defect-submit-count':
      setTimeout(renderDefectByUserChart, 0);
      break;
    case 'defect-overdue':
      setTimeout(renderDefectOverdueChart, 0);
      break;
    case 'defect-location-overdue':
      setTimeout(() => {
        // 获取数据并渲染定位超期图表
        fetchDefectDataAndRender('defect-location-overdue');
      }, 0);
      break;
    case 'defect-di':
      setTimeout(() => {
        // 获取数据并渲染DI统计图表
        fetchDefectDataAndRender('defect-di');
      }, 0);
      break;
    case 'defect-trend':
      setTimeout(loadDefectCumulativeChart, 0);
      break;
    case 'defect-avg-time':
      setTimeout(() => {
        // 获取数据并渲染平均定位时间
        fetchDefectDataAndRender('defect-avg-time');
      }, 0);
      break;
    case 'defect-module':
      setTimeout(() => {
        // 获取数据并渲染模块统计图表
        fetchDefectDataAndRender('defect-module');
      }, 0);
      break;
    case 'defect-submit-compare':
      setTimeout(() => {
        // 获取数据并渲染提单对比图表
        fetchDefectDataAndRender('defect-submit-compare');
      }, 0);
      break;
    case 'defect-resolution-rate':
      setTimeout(() => {
        // 获取数据并渲染解决率统计图表
        fetchDefectDataAndRender('defect-resolution-rate');
      }, 0);
      break;
  }
}

// 侧边栏菜单点击事件绑定
function bindPingCodeSidebar() {
  // 需求相关页面点击事件绑定
  document.querySelectorAll('[data-page="pingcode-demand"]').forEach(el => {
    el.addEventListener('click', () => showPingCodePage('pingcode-demand'));
  });
  document.querySelectorAll('[data-page="pingcode-demand-list"]').forEach(el => {
    el.addEventListener('click', () => showPingCodePage('pingcode-demand-list'));
  });
  document.querySelectorAll('[data-page="pingcode-demand-chart"]').forEach(el => {
    el.addEventListener('click', () => showPingCodePage('pingcode-demand-chart'));
  });
  document.querySelectorAll('[data-page="pingcode-iteration-distribution"]').forEach(el => {
    el.addEventListener('click', () => showPingCodePage('pingcode-iteration-distribution'));
  });
  document.querySelectorAll('[data-page="pingcode-iteration-progress"]').forEach(el => {
    el.addEventListener('click', () => showPingCodePage('pingcode-iteration-progress'));
  });
  document.querySelectorAll('[data-page="pingcode-person-progress"]').forEach(el => {
    el.addEventListener('click', () => showPingCodePage('pingcode-person-progress'));
  });
  
  // 问题单子菜单点击事件绑定
  document.querySelectorAll('[data-page="defect-list"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-list'));
  });
  document.querySelectorAll('[data-page="defect-submit-count"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-submit-count'));
  });
  document.querySelectorAll('[data-page="defect-overdue"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-overdue'));
  });
  document.querySelectorAll('[data-page="defect-location-overdue"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-location-overdue'));
  });
  document.querySelectorAll('[data-page="defect-di"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-di'));
  });
  document.querySelectorAll('[data-page="defect-trend"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-trend'));
  });
  document.querySelectorAll('[data-page="defect-avg-time"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-avg-time'));
  });
  document.querySelectorAll('[data-page="defect-module"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-module'));
  });
  document.querySelectorAll('[data-page="defect-submit-compare"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-submit-compare'));
  });
  document.querySelectorAll('[data-page="defect-resolution-rate"]').forEach(el => {
    el.addEventListener('click', () => showDefectPage('defect-resolution-rate'));
  });
}

document.addEventListener('DOMContentLoaded', bindPingCodeSidebar);

// 渲染表格和分页控件
function renderPingCodeTable(type) {
  const container = document.getElementById(type === 'demand' ? 'pingcode-demand' : 'pingcode-defect');
  container.innerHTML = '';
  let html = `<div class="bg-white rounded-xl shadow p-4">
    <div class="text-lg font-bold mb-4">PingCode${type === 'demand' ? '需求' : '缺陷'}列表</div>
    <table class="min-w-full text-sm text-left border whitespace-normal break-words">
      <thead><tr class='bg-gray-50'>
        <th class="px-2 py-2 border">编号</th>
        <th class="px-2 py-2 border">标题</th>
        <th class="px-2 py-2 border">状态</th>
        <th class="px-2 py-2 border">负责人</th>
        <th class="px-2 py-2 border">优先级</th>
        <th class="px-2 py-2 border">创建时间</th>
        <th class="px-2 py-2 border">截止时间</th>
      </tr></thead>
      <tbody id="pingcode-${type}-tbody">
        <!-- 数据行后续填充 -->
      </tbody>
    </table>
    <div id="pingcode-${type}-pagination" class="mt-4 flex justify-between items-center">
      <div class="text-sm text-gray-600">
        <span id="pagination-info-${type}">显示 0-0 条记录，共 0 条</span>
      </div>
      <div class="flex items-center gap-2">
        <button id="prev-page-${type}" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">上一页</button>
        <div id="page-numbers-${type}" class="flex gap-1"></div>
        <button id="next-page-${type}" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">下一页</button>
      </div>
    </div>
  </div>`;
  container.innerHTML = html;
}

// 拉取数据并渲染表格和分页
async function fetchPingCodeData(type, page = 1) {
  const api = type === 'demand' ? '/api/pingcode/demands' : '/api/pingcode/defects';
  const pageSize = 20;
  const tbodyId = `pingcode-${type}-tbody`;
  const paginationInfoId = `pagination-info-${type}`;
  const prevBtnId = `prev-page-${type}`;
  const nextBtnId = `next-page-${type}`;
  const pageNumbersId = `page-numbers-${type}`;

  document.getElementById(tbodyId).innerHTML = `<tr><td colspan="7" class="text-center text-gray-400">加载中...</td></tr>`;
  document.getElementById(paginationInfoId).textContent = '加载中...';

  try {
    const res = await fetch(api, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page, size: pageSize })
    });
    if (!res.ok) throw new Error('接口请求失败');
    const data = await res.json();
    const values = data.values || [];
    const total = data.total || 0;
    const tbody = document.getElementById(tbodyId);
    if (values.length === 0) {
      tbody.innerHTML = `<tr><td colspan="7" class="text-center text-gray-400">暂无数据</td></tr>`;
    } else {
      tbody.innerHTML = values.map(item => {
        const identifier = item.identifier || item.whole_identifier || item.id || 'N/A';
        const title = item.title || 'N/A';
        const state = item.state ? item.state.name : 'N/A';
        const assignee = item.assignee ? (item.assignee.display_name || item.assignee.name) : 'N/A';
        const priority = item.priority ? item.priority.name : 'N/A';
        const createdAt = item.created_at ? new Date(item.created_at * 1000).toLocaleDateString('zh-CN') : 'N/A';
        const endAt = item.end_at ? new Date(item.end_at * 1000).toLocaleDateString('zh-CN') : 'N/A';
        return `<tr>
          <td class='border px-2 py-2'>${identifier}</td>
          <td class='border px-2 py-2'>${title}</td>
          <td class='border px-2 py-2'>${state}</td>
          <td class='border px-2 py-2'>${assignee}</td>
          <td class='border px-2 py-2'>${priority}</td>
          <td class='border px-2 py-2'>${createdAt}</td>
          <td class='border px-2 py-2'>${endAt}</td>
        </tr>`;
      }).join('');
    }
    // 渲染分页
    const totalPages = Math.ceil(total / pageSize);
    const start = total === 0 ? 0 : (page - 1) * pageSize + 1;
    const end = Math.min(page * pageSize, total);
    document.getElementById(paginationInfoId).textContent = `显示 ${start}-${end} 条记录，共 ${total} 条`;
    // 上一页/下一页按钮
    const prevBtn = document.getElementById(prevBtnId);
    const nextBtn = document.getElementById(nextBtnId);
    prevBtn.disabled = page <= 1;
    nextBtn.disabled = page >= totalPages;
    prevBtn.onclick = () => { if (page > 1) fetchPingCodeData(type, page - 1); };
    nextBtn.onclick = () => { if (page < totalPages) fetchPingCodeData(type, page + 1); };
    // 页码按钮
    const pageNumbersDiv = document.getElementById(pageNumbersId);
    let pageNumbersHtml = '';
    const maxVisiblePages = 7;
    let startPage = Math.max(1, page - 3);
    let endPage = Math.min(totalPages, page + 3);
    if (endPage - startPage < maxVisiblePages - 1) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    if (startPage > 1) {
      pageNumbersHtml += `<button class="page-btn px-3 py-1 rounded bg-gray-200 hover:bg-gray-300" data-page="1">1</button>`;
      if (startPage > 2) pageNumbersHtml += `<span class="px-2 text-gray-500">...</span>`;
    }
    for (let i = startPage; i <= endPage; i++) {
      pageNumbersHtml += `<button class="page-btn px-3 py-1 rounded ${i === page ? 'bg-blue-500 text-white' : 'bg-gray-200 hover:bg-gray-300'}">${i}</button>`;
    }
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) pageNumbersHtml += `<span class="px-2 text-gray-500">...</span>`;
      pageNumbersHtml += `<button class="page-btn px-3 py-1 rounded bg-gray-200 hover:bg-gray-300" data-page="${totalPages}">${totalPages}</button>`;
    }
    pageNumbersDiv.innerHTML = pageNumbersHtml;
    pageNumbersDiv.querySelectorAll('.page-btn').forEach(btn => {
      btn.onclick = () => fetchPingCodeData(type, parseInt(btn.dataset.page));
    });
  } catch (e) {
    document.getElementById(tbodyId).innerHTML = `<tr><td colspan="7" class="text-center text-red-500">加载失败: ${e.message}</td></tr>`;
    document.getElementById(paginationInfoId).textContent = '加载失败';
  }
}

// 新的核心判断函数 - 基于提单领域
function isTestDomainDefect(defect) {
  const p = defect.properties || {};
  return p.shifoukaifaziti === "67b45951eca31ee2556321a5"; // 测试领域ID
}

function isDevelopmentDomainDefect(defect) {
  return !isTestDomainDefect(defect);
}

let defectChartInstance = null;

async function renderDefectByUserChart() {
  const chartDom = document.getElementById('defectByUserChart');
  if (!chartDom) return;
  // 插入标题
  chartDom.innerHTML = '<div style="font-size:18px;font-weight:bold;color:#165DFF;text-align:center;margin:0;">全部问题单提交次数统计</div>' +
    '<canvas id="defectByUserChartCanvas"></canvas>' +
    '<div style="text-align:left;color:#6b7280;font-size:14px;margin-top:8px;">点击柱状图查看详情</div>';
  const ctx = document.getElementById('defectByUserChartCanvas').getContext('2d');
  if (defectChartInstance) {
    defectChartInstance.destroy();
  }

  // 拉取所有缺陷数据
  let allDefects = [];
  let page = 1;
  let pageSize = 100;
  let total = 0;
  do {
    const res = await fetch('/api/pingcode/defects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page, size: pageSize })
    });
    if (!res.ok) break;
    const data = await res.json();
    const values = data.values || [];
    total = data.total || 0;
    allDefects = allDefects.concat(values);
    page++;
  } while (allDefects.length < total);

  // 按"创建人"统计每个人提交的缺陷总数
  const userMap = {};
  allDefects.forEach(item => {
    const creator = item.created_by ? (item.created_by.display_name || item.created_by.name) : '未知';
    userMap[creator] = (userMap[creator] || 0) + 1;
  });
  // 排序
  const users = Object.keys(userMap);
  const counts = users.map(u => userMap[u]);
  const sorted = users.map((u, i) => ({ name: u, count: counts[i] }))
    .sort((a, b) => b.count - a.count);
  const sortedUsers = sorted.map(i => i.name);
  const sortedCounts = sorted.map(i => i.count);

  // Chart.js 配置
  const data = {
    labels: sortedUsers,
    datasets: [{
      label: '提交次数',
      data: sortedCounts,
      backgroundColor: sortedCounts.map((_, i) => i === 0 ? 'rgba(59,130,246,0.8)' : 'rgba(59,130,246,0.5)'),
      borderColor: sortedCounts.map((_, i) => i === 0 ? 'rgba(59,130,246,1)' : 'rgba(59,130,246,0.7)'),
      borderWidth: 1,
      borderRadius: 6,
      barPercentage: 0.6,
      categoryPercentage: 0.7
    }]
  };
  const labelPlugin = {
    id: 'customLabel',
    afterDatasetsDraw: (chart) => {
      const { ctx, data, chartArea: { top }, scales: { x, y } } = chart;
      ctx.save();
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.font = 'bold 12px Arial';
      data.datasets[0].data.forEach((value, index) => {
        const xCoord = x.getPixelForValue(data.labels[index]);
        let yCoord = y.getPixelForValue(value);
        if (yCoord - 15 < top) yCoord = top + 15;
        ctx.fillStyle = '#333';
        ctx.fillText(value, xCoord, yCoord - 10);
      });
      ctx.restore();
    }
  };
  defectChartInstance = new Chart(ctx, {
    type: 'bar',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000, easing: 'easeOutQuart' },
      scales: {
        y: {
          beginAtZero: true,
          title: { display: true, text: '提交次数', font: { size: 14, weight: 'bold' } },
          grid: { color: 'rgba(0,0,0,0.05)' },
          ticks: { padding: 15 }
        },
        x: {
          title: { display: true, text: '人员', font: { size: 14, weight: 'bold' } },
          grid: { display: false },
          ticks: { padding: 10, autoSkip: true, maxRotation: 45, minRotation: 45 }
        }
      },
      plugins: {
        title: { display: false },
        legend: { display: false },
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.7)',
          titleFont: { size: 14, weight: 'bold' },
          bodyFont: { size: 13 },
          padding: 12,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              return `提交次数: ${context.raw}`;
            }
          }
        }
      }
    },
    plugins: [labelPlugin]
  });

  // 添加点击事件
  defectChartInstance.options.onClick = (event, elements) => {
    if (!elements.length) return;

    const clickedIndex = elements[0].index;
    const userName = sortedUsers[clickedIndex];

    // 筛选该用户创建的所有缺陷
    const userDefects = allDefects.filter(item => {
      const creator = item.created_by ? (item.created_by.display_name || item.created_by.name) : '未知';
      return creator === userName;
    });

    showDefectDetailsPage(userName, userDefects);
  };

  // 渲染主图表后，确保新图表容器存在并渲染超期图表
  let overdueDiv = document.getElementById('defectOverdueChart');
  if (!overdueDiv) {
    overdueDiv = document.createElement('div');
    overdueDiv.id = 'defectOverdueChart';
    overdueDiv.style = 'width:100%;height:400px;margin-top:32px;';
    const chartContainer = document.getElementById('defectByUserChart');
    if (chartContainer && chartContainer.parentNode) {
      chartContainer.parentNode.insertBefore(overdueDiv, chartContainer.nextSibling);
    }
  }
  await renderDefectOverdueChart();
}

// 在全部问题单提交次数统计图表下方添加新图表容器
const defectChartContainer = document.getElementById('defectByUserChart');
if (defectChartContainer && !document.getElementById('defectOverdueChart')) {
  const overdueDiv = document.createElement('div');
  overdueDiv.id = 'defectOverdueChart';
  overdueDiv.style = 'width:100%;height:400px;margin-top:32px;';
  defectChartContainer.parentNode.insertBefore(overdueDiv, defectChartContainer.nextSibling);
}

let defectOverdueChartInstance = null;

async function renderDefectOverdueChart() {
  const chartDom = document.getElementById('defectOverdueChart');
  if (!chartDom) return;
  chartDom.innerHTML = '<div style="font-size:18px;font-weight:bold;color:#165DFF;text-align:center;margin:0;">测试领域提交给开发的未关闭问题单超期统计</div>' +
    '<canvas id="defectOverdueChartCanvas"></canvas>';
  const ctx = document.getElementById('defectOverdueChartCanvas').getContext('2d');
  if (defectOverdueChartInstance) {
    defectOverdueChartInstance.destroy();
  }

  // 拉取所有缺陷数据
  let allDefects = [];
  let page = 1;
  let pageSize = 100;
  let total = 0;
  do {
    const res = await fetch('/api/pingcode/defects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page, size: pageSize })
    });
    if (!res.ok) break;
    const data = await res.json();
    const values = data.values || [];
    total = data.total || 0;
    allDefects = allDefects.concat(values);
    page++;
  } while (allDefects.length < total);

  // 获取每个问题单的流转历史，分析提交者和处理者
  let defectFlowAnalysis = {};
  // 设置为全局变量，供其他函数使用
  window.defectFlowAnalysis = defectFlowAnalysis;
  
  // 批量获取问题单的流转历史
  try {
    const defectIds = allDefects.map(defect => defect.id);
    const flowRes = await fetch('/api/pingcode/defect-flows', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ defectIds })
    });
    
    if (flowRes.ok) {
      const flowData = await flowRes.json();
      if (flowData.success && flowData.results) {
        defectFlowAnalysis = flowData.results;
        // 更新全局变量
        window.defectFlowAnalysis = defectFlowAnalysis;
      }
    }
  } catch (error) {
    console.warn('批量获取问题单流转历史失败，回退到基本分析:', error);
  }

  // 分析流转历史，识别测试提交给开发的问题单
  // 超期时间从第一次"提交审核"阶段开始计算
  const validDefects = [];
  const devSet = new Set();
  
  allDefects.forEach(item => {
    const flowHistory = defectFlowAnalysis[item.id];
    let hasSubmittedToDev = false;
    let developerName = null;
    let firstSubmitToDevTime = null;
    
    // 首先检查提单领域是否为测试
    if (!isTestDomainDefect(item)) {
      return; // 跳过非测试领域的问题单
    }
    
    // 如果有流转历史，尝试分析
    if (flowHistory && flowHistory.transitions) {
      // 分析流转历史
      flowHistory.transitions.forEach((transition, index) => {
        const fromState = transition.from_state ? transition.from_state.name : '';
        const toState = transition.to_state ? transition.to_state.name : '';
        const userName = transition.created_by ? (transition.created_by.display_name || transition.created_by.name) : '';
        const timestamp = transition.created_at || 0;
        
        // 第一次进入"提交审核"状态 = 测试第一次提交给开发
        if (toState.includes('提交审核') && !firstSubmitToDevTime) {
          firstSubmitToDevTime = timestamp * 1000;
          hasSubmittedToDev = true;
        }
        
        // 确定开发人员：优先从根因分析获取
        if (toState.includes('根因分析') && !developerName) {
          developerName = userName;
          devSet.add(userName);
        }
      });
      
      // 如果没有根因分析，使用当前负责人
      if (hasSubmittedToDev && !developerName) {
        developerName = item.assignee ? 
          (item.assignee.display_name || item.assignee.name) : '未分配';
        devSet.add(developerName);
      }
    }
    
    // 如果流转历史分析失败，回退到基本分析
    if (!hasSubmittedToDev) {
      const assignee = item.assignee ? (item.assignee.display_name || item.assignee.name) : '';
      
      // 提单领域已经是测试，如果有负责人，假设已经提交给开发
      if (assignee) {
        hasSubmittedToDev = true;
        developerName = assignee;
        devSet.add(assignee);
        // 回退时使用创建时间作为提交时间的近似值
        firstSubmitToDevTime = item.created_at ? item.created_at * 1000 : null;
      }
    }
    
    // 只有当确认已提交给开发且有开发人员时才添加到有效问题单列表
    if (hasSubmittedToDev && developerName && firstSubmitToDevTime) {
      validDefects.push({
        ...item,
        developerName,
        firstSubmitToDevTime
      });
    }
  });
  
  const devs = Array.from(devSet);

  // 统计每个开发的超期数量
  const now = Date.now();
  const statusClosed = '确认关闭';
  const overdueStats = {};
  devs.forEach(dev => {
    overdueStats[dev] = { yellow: 0, orange: 0, red: 0 };
  });
  
  validDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    if (state === statusClosed) return; // 跳过已关闭的问题单
    if (!item.firstSubmitToDevTime) return; // 跳过没有提交时间的问题单
    
    // 从第一次提交审核到当前时间的天数差
    const days = (now - item.firstSubmitToDevTime) / (1000 * 60 * 60 * 24);
    if (days > 30) {
      overdueStats[item.developerName].red++;      // 超30天 - 红色
    } else if (days > 7) {
      overdueStats[item.developerName].orange++;   // 超7天 - 橙色
    } else if (days > 3) {
      overdueStats[item.developerName].yellow++;   // 超3天 - 黄色
    }
    // days <= 3 不统计（未超期）
  });

  // 计算每个开发人员的总超期数量并排序
  const devTotalOverdue = devs.map(dev => ({
    name: dev,
    total: overdueStats[dev].yellow + overdueStats[dev].orange + overdueStats[dev].red,
    yellow: overdueStats[dev].yellow,
    orange: overdueStats[dev].orange,
    red: overdueStats[dev].red
  }));
  
  // 按总超期数量从高到低排序
  devTotalOverdue.sort((a, b) => b.total - a.total);
  
  // 横坐标为开发人（已排序）
  const labels = devTotalOverdue.map(dev => dev.name);
  const yellowData = devTotalOverdue.map(dev => dev.yellow);
  const orangeData = devTotalOverdue.map(dev => dev.orange);
  const redData = devTotalOverdue.map(dev => dev.red);
  
  // 检查是否有数据
  const totalData = yellowData.reduce((a, b) => a + b, 0) + 
                   orangeData.reduce((a, b) => a + b, 0) + 
                   redData.reduce((a, b) => a + b, 0);
  
  // 如果没有数据，显示提示信息
  if (totalData === 0) {
    const ctx = document.getElementById('defectOverdueChart');
    if (ctx) {
      ctx.style.display = 'flex';
      ctx.style.alignItems = 'center';
      ctx.style.justifyContent = 'center';
      ctx.style.height = '300px';
      ctx.style.backgroundColor = '#f8f9fa';
      ctx.style.border = '1px solid #dee2e6';
      ctx.style.borderRadius = '8px';
      ctx.innerHTML = '<div style="text-align: center; color: #6c757d;"><h4>暂无超期数据</h4><p>当前没有符合条件的问题单</p></div>';
      return;
    }
  }

  // 统计每个开发的超期问题单明细（按排序后的顺序）
  const overdueDetailMap = {};
  labels.forEach(dev => {
    overdueDetailMap[dev] = { yellow: [], orange: [], red: [] };
  });
  validDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    if (state === statusClosed) return;
    if (!item.firstSubmitToDevTime) return;
    
    const days = (now - item.firstSubmitToDevTime) / (1000 * 60 * 60 * 24);
    if (days > 30) {
      overdueDetailMap[item.developerName].red.push(item);
    } else if (days > 7) {
      overdueDetailMap[item.developerName].orange.push(item);
    } else if (days > 3) {
      overdueDetailMap[item.developerName].yellow.push(item);
    }
  });

  // Chart.js 配置
  const data = {
    labels: labels,
    datasets: [
      {
        label: '超3天',
        data: yellowData,
        backgroundColor: '#FFD234',
        borderColor: '#FFD234',
        borderWidth: 1,
        borderRadius: 6,
        barPercentage: 0.6,
        categoryPercentage: 0.7,
        maxBarThickness: 40
      },
      {
        label: '超7天',
        data: orangeData,
        backgroundColor: '#FFA415',
        borderColor: '#FFA415',
        borderWidth: 1,
        borderRadius: 6,
        barPercentage: 0.6,
        categoryPercentage: 0.7,
        maxBarThickness: 40
      },
      {
        label: '超30天',
        data: redData,
        backgroundColor: '#FA5A55',
        borderColor: '#FA5A55',
        borderWidth: 1,
        borderRadius: 6,
        barPercentage: 0.6,
        categoryPercentage: 0.7,
        maxBarThickness: 40
      }
    ]
  };
  const labelPlugin = {
    id: 'customLabel',
    afterDatasetsDraw: (chart) => {
      const { ctx, data, chartArea: { top }, scales: { x, y } } = chart;
      ctx.save();
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.font = 'bold 12px Arial';
      data.datasets.forEach((ds, dsIdx) => {
        ds.data.forEach((value, index) => {
          if (!value) return;
          const xCoord = x.getPixelForValue(data.labels[index]) + (dsIdx - 1) * (x.getPixelForValue(data.labels[1] || 1) - x.getPixelForValue(data.labels[0] || 0)) / 4;
          let yCoord = y.getPixelForValue(value);
          if (yCoord - 15 < top) yCoord = top + 15;
          ctx.fillStyle = '#333';
          ctx.fillText(value, xCoord, yCoord - 10);
        });
      });
      ctx.restore();
    }
  };
  defectOverdueChartInstance = new Chart(ctx, {
    type: 'bar',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000, easing: 'easeOutQuart' },
      scales: {
        y: {
          beginAtZero: true,
          title: { display: true, text: '超期数量', font: { size: 14, weight: 'bold' } },
          grid: { color: 'rgba(0,0,0,0.05)' },
          ticks: {
            padding: 15,
            precision: 0,
            callback: function(value) { return Number.isInteger(value) ? value : null; }
          }
        },
        x: {
          title: { display: true, text: '开发人员', font: { size: 14, weight: 'bold' } },
          grid: { display: false },
          ticks: { padding: 10, autoSkip: true, maxRotation: 45, minRotation: 45 }
        }
      },
      plugins: {
        title: { display: false },
        legend: { display: true, position: 'top' },
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.7)',
          titleFont: { size: 14, weight: 'bold' },
          bodyFont: { size: 13 },
          padding: 12,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              return `${context.dataset.label}: ${context.raw}`;
            }
          }
        }
      },
      onClick: (event, elements, chart) => {
        if (!elements.length) return;
        const element = elements[0];
        const devIdx = element.index;
        const dsIdx = element.datasetIndex;
        const devName = labels[devIdx];
        const typeKey = dsIdx === 0 ? 'yellow' : dsIdx === 1 ? 'orange' : 'red';
        const detailList = overdueDetailMap[devName][typeKey];
        if (detailList && detailList.length) {
          showDefectDetailsPage(devName + ' - ' + (typeKey === 'yellow' ? '超3天' : typeKey === 'orange' ? '超7天' : '超30天'), detailList);
        }
      }
    },
    plugins: [labelPlugin]
  });

}

function renderDevPhaseOverdueChart(allDefects, defectFlowAnalysis) {
  const canvas = document.getElementById('devPhaseOverdueChartCanvas');
  if (!canvas) return;
  const ctx = canvas.getContext('2d');
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  if (window.devPhaseOverdueChartInstance) {
    window.devPhaseOverdueChartInstance.destroy();
  }
  // 统计区间和明细
  let count3 = 0, count7 = 0, count14 = 0, count30 = 0;
  const detailMap = { '3': [], '7': [], '14': [], '30': [] };
  allDefects.forEach(item => {
    
    const flowHistory = defectFlowAnalysis[item.id];
    if (!flowHistory || !flowHistory.transitions) return;
    let submitTime = null;
    let devStartTime = null;
    let regressionTime = null;
    for (let i = 0; i < flowHistory.transitions.length; i++) {
      const transition = flowHistory.transitions[i];
      if (!submitTime && transition.to_state && transition.to_state.name && transition.to_state.name.includes('提交审核')) {
        submitTime = transition.created_at * 1000;
        if (flowHistory.transitions[i + 1]) {
          devStartTime = flowHistory.transitions[i + 1].created_at * 1000;
        }
      }
      if (!regressionTime && transition.to_state && transition.to_state.name && transition.to_state.name.includes('回归测试')) {
        regressionTime = transition.created_at * 1000;
      }
    }
    if (devStartTime && regressionTime && regressionTime > devStartTime) {
      const days = (regressionTime - devStartTime) / (1000 * 60 * 60 * 24);
      if (days > 30) { count30++; detailMap['30'].push(item); }
      else if (days > 14) { count14++; detailMap['14'].push(item); }
      else if (days > 7) { count7++; detailMap['7'].push(item); }
      else if (days > 3) { count3++; detailMap['3'].push(item); }
    }
  });
  const data = {
    labels: ['超3天', '超7天', '超14天', '超30天'],
    datasets: [{
      label: '超期数量',
      data: [count3, count7, count14, count30],
      backgroundColor: ['#FFD234', '#FFA415', '#FA5A55', '#B620E0'],
      borderRadius: 6,
      barPercentage: 0.6,
      categoryPercentage: 0.7
    }]
  };
  window.devPhaseOverdueChartInstance = new Chart(ctx, {
    type: 'bar',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000, easing: 'easeOutQuart' },
      plugins: { legend: { display: false } },
      scales: {
        y: { beginAtZero: true, title: { display: true, text: '超期数量', font: { size: 14, weight: 'bold' } }, ticks: { padding: 15 } },
        x: { title: { display: true, text: '区间', font: { size: 14, weight: 'bold' } }, ticks: { padding: 10, autoSkip: true, maxRotation: 45, minRotation: 45 } }
      },
      onClick: (event, elements) => {
        if (!elements.length) return;
        const idx = elements[0].index;
        const label = data.labels[idx];
        let key = '3';
        if (label === '超7天') key = '7';
        else if (label === '超14天') key = '14';
        else if (label === '超30天') key = '30';
        const detailList = detailMap[key];
        if (detailList && detailList.length) {
          showDefectDetailsPage(label + ' - 开发定位环节超期明细', detailList);
        }
      }
    },
    plugins: [{
      afterDatasetsDraw: function(chart) {
        const { ctx, data, chartArea: { top }, scales: { x, y } } = chart;
        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.font = 'bold 14px Arial';
        ctx.fillStyle = '#333';
        data.datasets[0].data.forEach((value, index) => {
          if (value > 0) {
            const xCoord = x.getPixelForValue(data.labels[index]);
            let yCoord = y.getPixelForValue(value);
            if (yCoord - 15 < top) yCoord = top + 15;
            ctx.fillText(value, xCoord, yCoord - 10);
          }
        });
        ctx.restore();
      }
    }]
  });
}

function renderDIBarCharts(allDefects, defectFlowAnalysis) {
  // DI分值映射
  const severityScore = {
    "致命": 10,
    "严重": 3,
    "一般": 1,
    "提示": 0.1
  };
  const statusClosed = '确认关闭';
  const severityLabels = ['致命', '严重', '一般', '提示'];
  
  // 统计未关闭
  const diTotal = { "致命": 0, "严重": 0, "一般": 0, "提示": 0 };
  const diTotalDetail = { "致命": [], "严重": [], "一般": [], "提示": [] };
  allDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    if (state === statusClosed) return;
    const p = item.properties || {};
    const severity = DICT.severity[p.severity] || '';
    if (severityScore[severity]) {
      diTotal[severity] += severityScore[severity];
      diTotalDetail[severity].push(item);
    }
  });
  
  // 统计开发环节
  const diDev = { "致命": 0, "严重": 0, "一般": 0, "提示": 0 };
  const diDevDetail = { "致命": [], "严重": [], "一般": [], "提示": [] };
  let devCount = 0;
  allDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    if (state === statusClosed) return;
    
    // 只统计开发领域的问题单（非测试领域）
    const flowHistory = defectFlowAnalysis[item.id];
    if (!flowHistory || !flowHistory.transitions) return;
    
    // 判断是否在开发环节（提交审核后有下一个节点）
    let found = false;
    
    // 检查flowHistory.transitions是否存在
    if (!flowHistory.transitions) {
      return;
    }
    
    for (let i = 0; i < flowHistory.transitions.length; i++) {
      const transition = flowHistory.transitions[i];
      
      // 检查to_state.name字段是否包含"提交审核"
      if (transition.to_state && transition.to_state.name && transition.to_state.name.includes('提交审核')) {
        if (flowHistory.transitions[i + 1]) {
          found = true;
        }
        break;
      }
    }
    
    
    if (!found) return;
    devCount++;
    const p = item.properties || {};
    const severity = DICT.severity[p.severity] || '';
    if (severityScore[severity]) {
      diDev[severity] += severityScore[severity];
      diDevDetail[severity].push(item);
    }
  });
  
  // 渲染未关闭DI统计
  const totalCtx = document.getElementById('diTotalBarChart');
  if (!totalCtx) return;
  const totalCtx2d = totalCtx.getContext('2d');
  if (window.diTotalBarChartInstance) window.diTotalBarChartInstance.destroy();
  window.diTotalBarChartInstance = new Chart(totalCtx2d, {
    type: 'bar',
    data: {
      labels: severityLabels,
      datasets: [{
        label: 'DI分值总和',
        data: severityLabels.map(l => diTotal[l]),
        backgroundColor: ['#F43F5E', '#F59E42', '#3B82F6', '#10B981'],
        borderColor: ['#F43F5E', '#F59E42', '#3B82F6', '#10B981'],
        borderWidth: 2,
        borderRadius: 6,
        barPercentage: 0.6,
        categoryPercentage: 0.7,
        maxBarThickness: 60
      }]
    },
    options: {
      responsive: false,
      maintainAspectRatio: false,
      plugins: { 
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: function(context) {
              return 'DI分值: ' + context.parsed.y.toFixed(1);
            }
          }
        }
      },
      scales: {
        y: { beginAtZero: true, title: { display: true, text: 'DI分值' }, ticks: { precision: 0 } },
        x: { title: { display: false }, ticks: { font: { size: 16 } } }
      },
      onClick: (event, elements) => {
        if (!elements.length) return;
        const idx = elements[0].index;
        const label = severityLabels[idx];
        showDefectDetailsPage('未关闭DI-' + label, diTotalDetail[label]);
      }
    },
    plugins: [{
      afterDatasetsDraw: function(chart) {
        const { ctx, data, chartArea: { top }, scales: { x, y } } = chart;
        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.font = 'bold 12px Arial';
        data.datasets[0].data.forEach((value, index) => {
          if (value > 0) {
            const xCoord = x.getPixelForValue(data.labels[index]);
            let yCoord = y.getPixelForValue(value);
            if (yCoord - 15 < top) yCoord = top + 15;
            ctx.fillStyle = '#333';
            ctx.fillText(value.toFixed(1), xCoord, yCoord - 10);
          }
        });
        ctx.restore();
      }
    }]
  });
  
  // 渲染开发环节DI统计
  const devCtx = document.getElementById('diDevBarChart');
  if (!devCtx) return;
  const devCtx2d = devCtx.getContext('2d');
  if (window.diDevBarChartInstance) window.diDevBarChartInstance.destroy();
  window.diDevBarChartInstance = new Chart(devCtx2d, {
    type: 'bar',
    data: {
      labels: severityLabels,
      datasets: [{
        label: 'DI分值总和',
        data: severityLabels.map(l => diDev[l]),
        backgroundColor: ['#1C90F3', '#F59E42', '#3B82F6', '#10B981'],
        borderColor: ['#1C90F3', '#F59E42', '#3B82F6', '#10B981'],
        borderWidth: 2,
        borderRadius: 6,
        barPercentage: 0.6,
        categoryPercentage: 0.7,
        maxBarThickness: 60
      }]
    },
    options: {
      responsive: false,
      maintainAspectRatio: false,
      plugins: { 
        legend: { display: false },
        tooltip: {
          callbacks: {
            label: function(context) {
              return 'DI分值: ' + context.parsed.y.toFixed(1);
            }
          }
        }
      },
      scales: {
        y: { beginAtZero: true, title: { display: true, text: 'DI分值' }, ticks: { precision: 0 } },
        x: { title: { display: false }, ticks: { font: { size: 16 } } }
      },
      onClick: (event, elements) => {
        if (!elements.length) return;
        const idx = elements[0].index;
        const label = severityLabels[idx];
        showDefectDetailsPage('开发环节DI-' + label, diDevDetail[label]);
      }
    },
    plugins: [{
      afterDatasetsDraw: function(chart) {
        const { ctx, data, chartArea: { top }, scales: { x, y } } = chart;
        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
        ctx.font = 'bold 12px Arial';
        data.datasets[0].data.forEach((value, index) => {
          if (value > 0) {
            const xCoord = x.getPixelForValue(data.labels[index]);
            let yCoord = y.getPixelForValue(value);
            if (yCoord - 15 < top) yCoord = top + 15;
            ctx.fillStyle = '#333';
            ctx.fillText(value.toFixed(1), xCoord, yCoord - 10);
          }
        });
        ctx.restore();
      }
    }]
  });
}

// 获取缺陷数据并渲染对应图表
async function fetchDefectDataAndRender(pageType) {
  // 拉取所有缺陷数据
  let allDefects = [];
  let page = 1;
  let pageSize = 100;
  let total = 0;
  
  try {
    do {
      const res = await fetch('/api/pingcode/defects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ page, size: pageSize })
      });
      if (!res.ok) break;
      const data = await res.json();
      const values = data.values || [];
      total = data.total || 0;
      allDefects = allDefects.concat(values);
      page++;
    } while (allDefects.length < total);

    // 获取流转历史数据
    let defectFlowAnalysis = {};
    try {
      const defectIds = allDefects.map(defect => defect.id);
      const flowRes = await fetch('/api/pingcode/defect-flows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ defectIds })
      });
      
      if (flowRes.ok) {
        const flowData = await flowRes.json();
        if (flowData.success && flowData.results) {
          defectFlowAnalysis = flowData.results;
          window.defectFlowAnalysis = defectFlowAnalysis;
        }
      }
    } catch (error) {
      console.warn('获取问题单流转历史失败:', error);
    }

    // 根据页面类型渲染对应图表
    switch(pageType) {
      case 'defect-location-overdue':
        renderDevPhaseOverdueChart(allDefects, defectFlowAnalysis);
        break;
      case 'defect-di':
        renderDIBarCharts(allDefects, defectFlowAnalysis);
        break;
      case 'defect-avg-time':
        renderAverageLocationTimeChart(allDefects, defectFlowAnalysis);
        break;
      case 'defect-module':
        renderModuleDefectChart(allDefects);
        break;
      case 'defect-submit-compare':
        renderSubmitterComparisonChart(allDefects);
        break;
      case 'defect-resolution-rate':
        renderResolutionRateChart(allDefects);
        break;
    }
  } catch (error) {
    console.error('获取缺陷数据失败:', error);
  }
}



// 新增：累计问题单曲线图渲染
async function loadDefectCumulativeChart() {
  const chartDom = document.getElementById('defectCumulativeChart');
  if (!chartDom) return;
  const myChart = echarts.init(chartDom);
  myChart.showLoading();
  try {
    const res = await fetch('/api/pingcode/defect-daily-stats');
    const data = await res.json();
    
    // 过滤从6月2日开始的数据
    const filteredData = data.filter(item => {
      const itemDate = new Date(item.date);
      const startDate = new Date('2025-06-02'); // 从6月2日开始显示
      return itemDate >= startDate;
    });
    
    const dates = filteredData.map(item => item.date);
    const createdArr = filteredData.map(item => item.created);
    const resolvedArr = filteredData.map(item => item.resolved);
    
    // 计算累计未关闭数据
    const unclosedArr = createdArr.map((created, index) => created - resolvedArr[index]);
    
    const option = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['累计发现', '累计解决', '累计未关闭'] },
      xAxis: { 
        type: 'category', 
        data: dates,
        axisLabel: {
          interval: 29, // 大约每30天显示一个标签（按月间隔）
          formatter: function(value) {
            // 将日期字符串转换为Date对象，然后格式化为年-月
            try {
              const date = new Date(value);
              if (isNaN(date.getTime())) return value;
              return date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0');
            } catch (e) {
              return value;
            }
          },
          rotate: 45,
          margin: 10
        },
        axisTick: {
          alignWithLabel: true
        }
      },
      yAxis: { type: 'value', name: '数量' },
      series: [
        { name: '累计发现', type: 'line', data: createdArr, smooth: true, symbol: 'circle' },
        { name: '累计解决', type: 'line', data: resolvedArr, smooth: true, symbol: 'circle' },
        { 
          name: '累计未关闭', 
          type: 'line', 
          data: unclosedArr, 
          smooth: true, 
          symbol: 'circle',
          lineStyle: { color: '#DC2626' },
          itemStyle: { color: '#DC2626' }
        }
      ]
    };
    myChart.setOption(option);
    myChart.hideLoading();
  } catch (e) {
    myChart.hideLoading();
    chartDom.innerHTML = '<div class="text-red-500 text-center py-8">加载数据失败</div>';
  }
}


// 替换 showDefectDetailsModal 为 showDefectDetailsPage
function showDefectDetailsPage(userName, defects) {
  const detailPage = document.getElementById('defectDetailPage');
  const mainContent = document.getElementById('main-content');
  const title = document.getElementById('defectDetailPageTitle');
  const content = document.getElementById('defectDetailPageContent');
  const pagination = document.getElementById('defectDetailPagePagination');
  const closeBtn = document.getElementById('closeDefectDetailPage');

  mainContent.classList.add('hidden');
  detailPage.classList.remove('hidden');
  title.textContent = `${userName} (共 ${defects.length} 条)`;

  closeBtn.onclick = () => {
    detailPage.classList.add('hidden');
    mainContent.classList.remove('hidden');
  };

  if (defects.length === 0) {
    content.innerHTML = '<div class="text-center text-gray-500 py-8">暂无数据</div>';
    pagination.innerHTML = '';
    return;
  }

  const sortedDefects = defects.sort((a, b) => new Date(b.created_at * 1000) - new Date(a.created_at * 1000));
  const pageSize = 20;
  let currentPage = 1;

  function renderPage() {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    const pageList = sortedDefects.slice(start, end);

    content.innerHTML = `
      <div class="border border-gray-200 rounded-lg overflow-auto">
        <table class="w-full text-sm">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">编号</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">标题</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">状态</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">负责人</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">优先级</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">产品程序</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">提单人</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">是否用例发现</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">提单领域</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">项目阶段</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">问题归属领域</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">复现概率</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">原因分析</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">解决方案</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">回归测试结论</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">关闭类型</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">计划关闭时间</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">创建时间</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">当前责任人</th>
              <th class="px-2 py-2 text-left font-semibold text-gray-700">定位时间（天）</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            ${pageList.map(defect => {
              const p = defect.properties || {};
              return `<tr class="hover:bg-gray-50 transition-colors">
                <td class="px-2 py-2 text-gray-600">${defect.identifier || defect.whole_identifier || defect.id || 'N/A'}</td>
                <td class="px-2 py-2">${defect.title || 'N/A'}</td>
                <td class="px-2 py-2 text-gray-600">${defect.state ? defect.state.name : ''}</td>
                <td class="px-2 py-2 text-gray-600">${defect.assignee ? (defect.assignee.display_name || defect.assignee.name) : ''}</td>
                <td class="px-2 py-2 text-gray-600">${defect.priority ? defect.priority.name : ''}</td>
                <td class="px-2 py-2 text-gray-600">
                  ${
                    Array.isArray(p.wentileibie)
                      ? (p.wentileibie.length
                          ? (DICT.wentileibie[p.wentileibie[0]] ? DICT.wentileibie[p.wentileibie[0]] : '')
                          : '')
                      : (p.wentileibie
                          ? (DICT.wentileibie[p.wentileibie] ? DICT.wentileibie[p.wentileibie] : '')
                          : '')
                  }
                </td>
                <td class="px-2 py-2 text-gray-600">${defect.created_by ? (defect.created_by.display_name || defect.created_by.name) : ''}</td>
                <td class="px-2 py-2 text-gray-600">${DICT.shifouyonglifaxian[p.shifouyonglifaxian] || p.shifouyonglifaxian || ''}</td>
                <td class="px-2 py-2 text-gray-600">
                  ${Array.isArray(p.shifoukaifaziti)
                    ? (p.shifoukaifaziti.length ? (DICT.shifoukaifaziti[p.shifoukaifaziti[0]] || p.shifoukaifaziti[0]) : '')
                    : (p.shifoukaifaziti ? (DICT.shifoukaifaziti[p.shifoukaifaziti] || p.shifoukaifaziti) : '')}
                </td>
                <td class="px-2 py-2 text-gray-600">${DICT.xiangmujieduan[p.xiangmujieduan] || p.xiangmujieduan || ''}</td>
                <td class="px-2 py-2 text-gray-600">
                  ${
                    Array.isArray(p.kaifamokuai)
                      ? (p.kaifamokuai.length
                          ? (DICT.kaifamokuai[p.kaifamokuai[0]] ? DICT.kaifamokuai[p.kaifamokuai[0]] : '')
                          : '')
                      : (p.kaifamokuai
                          ? (DICT.kaifamokuai[p.kaifamokuai] ? DICT.kaifamokuai[p.kaifamokuai] : '')
                          : '')
                  }
                </td>
                <td class="px-2 py-2 text-gray-600">${DICT.reappear_probability[p.reappear_probability] || p.reappear_probability || ''}</td>
                <td class="px-2 py-2 text-gray-600">${DICT.reason[p.reason] || p.reason || ''}</td>
                <td class="px-2 py-2 text-gray-600">${DICT.solution[p.solution] || p.solution || ''}</td>
                <td class="px-2 py-2 text-gray-600">${DICT.huiguiceshijielun[p.huiguiceshijielun] || p.huiguiceshijielun || ''}</td>
                <td class="px-2 py-2 text-gray-600">${DICT.guanbileixing[p.guanbileixing] || p.guanbileixing || ''}</td>
                <td class="px-2 py-2 text-gray-600">${p.jihuaguanbishijian ? new Date(p.jihuaguanbishijian * 1000).toLocaleString('zh-CN') : ''}</td>
                <td class="px-2 py-2 text-gray-600">${defect.created_at ? new Date(defect.created_at * 1000).toLocaleDateString('zh-CN') : ''}</td>
                <td class="px-2 py-2 text-gray-600"><span style="color: #165DFF; font-weight: bold;">${getCurrentResponsiblePerson(defect) || ''}</span></td>
                <td class="px-2 py-2 text-gray-600">${defect.locationTime ? defect.locationTime.toFixed(1) : ''}</td>
              </tr>`;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;



    // 分页
    const totalPages = Math.ceil(sortedDefects.length / pageSize);
    pagination.innerHTML = '';
    if (totalPages > 1) {
      for (let i = 1; i <= totalPages; i++) {
        const btn = document.createElement('button');
        btn.textContent = i;
        btn.className = `px-4 py-2 rounded text-sm font-medium transition-colors ${i === currentPage ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`;
        btn.onclick = () => {
          currentPage = i;
          renderPage();
        };
        pagination.appendChild(btn);
      }
    }
  }

  renderPage();
}

// 新增：获取当前责任人的函数
function getCurrentResponsiblePerson(defect) {
  // 从全局变量中获取流转历史
  if (window.defectFlowAnalysis && window.defectFlowAnalysis[defect.id]) {
    const flowHistory = window.defectFlowAnalysis[defect.id];
    if (flowHistory.transitions && flowHistory.transitions.length > 0) {
      // 获取最后一条流转记录（最新的状态变更）
      const lastTransition = flowHistory.transitions[flowHistory.transitions.length - 1];
      if (lastTransition.created_by) {
        return lastTransition.created_by.display_name || lastTransition.created_by.name;
      }
    }
  }
  return '';
}

// 弹出属性详情弹窗
function showDefectAttrModal(defect) {
  // 彩色标签
  function badge(text, color) {
    return text ? `<span style="display:inline-block;padding:2px 10px;border-radius:12px;font-size:13px;font-weight:500;color:#fff;background:${color};margin-right:8px;">${text}</span>` : '';
  }
  // 头像
  function avatar(url, name) {
    if (!url) return '';
    return `<img src="${url}" alt="${name}" style="width:28px;height:28px;border-radius:50%;display:inline-block;vertical-align:middle;margin-right:6px;">`;
  }
  // 颜色映射
  const colorMap = {
    '高': '#f43f5e', '中': '#f59e42', '低': '#3b82f6', '软性': '#6366f1',
    '稳定性问题': '#ec4899', '功能性问题': '#f59e42', '提示': '#10b981',
    '用例发现': '#ef4444', '固件': '#f59e42', 'TR4/TRA4 EVT': '#fbbf24',
    '普通': '#64748b', '最高': '#f43f5e', '较高': '#f59e42', '较低': '#3b82f6',
    '严重': '#f43f5e', '一般': '#f59e42', '建议': '#6366f1', '未知': '#64748b'
  };

  // 字段渲染
  const fields = [
    { label: '优先级', value: defect.priority?.name, color: colorMap[defect.priority?.name] || '#3b82f6' },
    
    { label: '严重程度', value: defect.severity, color: colorMap[defect.severity] || '#10b981' },
    { label: '提单人', value: (defect.created_by ? avatar(defect.created_by.avatar_url, defect.created_by.display_name || defect.created_by.name) + (defect.created_by.display_name || defect.created_by.name) : '') },
    { label: '是否用例发现', value: defect.case_found, color: colorMap[defect.case_found] || '#ef4444' },
    { label: '提单领域', value: defect.domain, color: colorMap[defect.domain] || '#f59e42' },
    { label: '项目阶段', value: defect.project_stage, color: colorMap[defect.project_stage] || '#fbbf24' },
    { label: '问题归属领域', value: defect.belong_domain, color: colorMap[defect.belong_domain] || '#f59e42' },
    { label: '复现概率', value: defect.reproduce_rate },
    { label: '原因分析', value: defect.cause_analysis },
    { label: '解决方案', value: defect.solution },
    { label: '回归测试结论', value: defect.review_conclusion },
    { label: '关闭类型', value: defect.close_type },
    { label: '关闭时间', value: defect.closed_at ? new Date(defect.closed_at * 1000).toLocaleString('zh-CN') : '' }
  ];

  // 构造弹窗内容
  const html = `
    <div id="defectAttrModal" class="fixed inset-0 z-60 flex items-center justify-center bg-black bg-opacity-30">
      <div class="bg-white rounded-xl shadow-xl p-8 max-w-md w-full relative">
        <button id="closeDefectAttrModal" class="absolute top-4 right-4 text-2xl text-gray-400 hover:text-gray-600">&times;</button>
        <h3 class="text-lg font-bold mb-4">属性</h3>
        <div class="space-y-3">
          ${fields.map(f => `
            <div style="display:flex;align-items:center;">
              <span class="font-semibold" style="width:110px;display:inline-block;">${f.label}:</span>
              ${f.color ? badge(f.value, f.color) : (f.value || '')}
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;
  // 移除已存在弹窗
  document.getElementById('defectAttrModal')?.remove();
  // 插入弹窗
  document.body.insertAdjacentHTML('beforeend', html);
  document.getElementById('closeDefectAttrModal').onclick = () => {
    document.getElementById('defectAttrModal').remove();
  };
}

// 新增：渲染平均定位时间统计图表
function renderAverageLocationTimeChart(allDefects, defectFlowAnalysis) {
  // 只统计测试领域的问题单
  const testDomainDefects = allDefects.filter(item => isTestDomainDefect(item));
  const displayElement = document.getElementById('averageLocationTimeValue');
  if (!displayElement) return;

  // 统计所有问题单的定位时间
  let totalLocationTime = 0;  // 所有问题单定位时间总和
  let validDefectCount = 0;   // 有效问题单数量
  const allLocationDetails = [];  // 所有定位时间详情
  
  testDomainDefects.forEach(item => {
    const flowHistory = defectFlowAnalysis[item.id];
    if (!flowHistory || !flowHistory.transitions) return;
    
    let firstDevTime = null;  // 第一次进入开发环节的时间
    let lastRegressionTime = null;  // 最后一次进入回归测试的时间
    
    // 分析流转历史
    flowHistory.transitions.forEach((transition, index) => {
      const toState = transition.to_state ? transition.to_state.name : '';
      const timestamp = transition.created_at || 0;
      
      // 识别第一次进入开发环节（提交审核后的下一个流转）
      if (toState.includes('提交审核')) {
        // 找到提交审核，下一个流转就是进入开发环节
        if (flowHistory.transitions[index + 1]) {
          const nextTransition = flowHistory.transitions[index + 1];
          firstDevTime = nextTransition.created_at * 1000;
        }
      }
      
      // 识别最后一次进入回归测试
      if (toState.includes('回归测试')) {
        lastRegressionTime = timestamp * 1000;
      }
    });
    
    // 如果找到了有效的定位时间
    if (firstDevTime && lastRegressionTime && lastRegressionTime > firstDevTime) {
      const locationTime = (lastRegressionTime - firstDevTime) / (1000 * 60 * 60 * 24); // 转换为天
      
      totalLocationTime += locationTime;
      validDefectCount++;
      
      allLocationDetails.push({
        ...item,
        locationTime: locationTime,
        firstDevTime: firstDevTime,
        lastRegressionTime: lastRegressionTime
      });
    }
  });
  
  // 计算整体平均定位时间
  const averageLocationTime = validDefectCount > 0 ? totalLocationTime / validDefectCount : 0;
  
  
  
  // 检查是否有数据
  if (validDefectCount === 0) {
    const chartDom = document.getElementById('averageLocationTimeChartWrapper');
    if (chartDom) {
      chartDom.style.display = 'flex';
      chartDom.style.alignItems = 'center';
      chartDom.style.justifyContent = 'center';
      chartDom.style.height = '300px';
      chartDom.style.backgroundColor = '#f8f9fa';
      chartDom.style.border = '1px solid #dee2e6';
      chartDom.style.borderRadius = '8px';
      chartDom.innerHTML = '<div style="text-align: center; color: #6c757d;"><h4>暂无定位时间数据</h4><p>当前没有符合条件的问题单</p></div>';
      return;
    }
  }
  
  // 更新显示数值（保留两位小数）
  displayElement.textContent = averageLocationTime.toFixed(2);
  
  // 添加点击事件，显示所有定位时间详情
  const displayContainer = document.getElementById('averageLocationTimeDisplay');
  if (displayContainer) {
    displayContainer.style.cursor = 'pointer';
    displayContainer.onclick = () => {
      if (allLocationDetails.length > 0) {
        showDefectDetailsPage('整体平均定位时间明细', allLocationDetails);
      }
    };
  }
}

// 新增：渲染按UFS模块统计图表
function renderModuleDefectChart(allDefects) {
  const canvas = document.getElementById('moduleDefectChartCanvas');
  if (!canvas) return;
  const ctx = canvas.getContext('2d');
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  if (window.moduleDefectChartInstance) {
    window.moduleDefectChartInstance.destroy();
  }

  // 按UFS模块统计问题单数量
  const moduleStats = {};
  const moduleDetails = {};
  
  allDefects.forEach(item => {
    const p = item.properties || {};
    const moduleId = p.UFSmokuai;
    const moduleName = UFS_MODULES[moduleId] || '未知模块';
    
    if (!moduleStats[moduleName]) {
      moduleStats[moduleName] = 0;
      moduleDetails[moduleName] = [];
    }
    
    moduleStats[moduleName]++;
    moduleDetails[moduleName].push(item);
  });
  
  // 按数量排序
  const sortedModules = Object.keys(moduleStats).sort((a, b) => moduleStats[b] - moduleStats[a]);
  const moduleCounts = sortedModules.map(module => moduleStats[module]);
  
  
  
  // 检查是否有数据
  if (sortedModules.length === 0) {
    const chartDom = document.getElementById('moduleDefectChartWrapper');
    if (chartDom) {
      chartDom.style.display = 'flex';
      chartDom.style.alignItems = 'center';
      chartDom.style.justifyContent = 'center';
      chartDom.style.height = '300px';
      chartDom.style.backgroundColor = '#f8f9fa';
      chartDom.style.border = '1px solid #dee2e6';
      chartDom.style.borderRadius = '8px';
      chartDom.innerHTML = '<div style="text-align: center; color: #6c757d;"><h4>暂无UFS模块数据</h4><p>当前没有符合条件的问题单</p></div>';
      return;
    }
  }
  
  // Chart.js 配置
  const data = {
    labels: sortedModules,
    datasets: [{
      label: '问题单数量',
      data: moduleCounts,
      backgroundColor: moduleCounts.map((_, i) => i === 0 ? 'rgba(59,130,246,0.8)' : 'rgba(59,130,246,0.5)'),
      borderColor: moduleCounts.map((_, i) => i === 0 ? 'rgba(59,130,246,1)' : 'rgba(59,130,246,0.7)'),
      borderWidth: 1,
      borderRadius: 6,
      barPercentage: 0.6,
      categoryPercentage: 0.7
    }]
  };
  
  const labelPlugin = {
    id: 'customLabel',
    afterDatasetsDraw: (chart) => {
      const { ctx, data, chartArea: { top }, scales: { x, y } } = chart;
      ctx.save();
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.font = 'bold 12px Arial';
      data.datasets[0].data.forEach((value, index) => {
        const xCoord = x.getPixelForValue(data.labels[index]);
        let yCoord = y.getPixelForValue(value);
        if (yCoord - 15 < top) yCoord = top + 15;
        ctx.fillStyle = '#333';
        ctx.fillText(value, xCoord, yCoord - 10);
      });
      ctx.restore();
    }
  };
  
  window.moduleDefectChartInstance = new Chart(ctx, {
    type: 'bar',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000, easing: 'easeOutQuart' },
      scales: {
        y: {
          beginAtZero: true,
          title: { display: true, text: '问题单数量', font: { size: 14, weight: 'bold' } },
          grid: { color: 'rgba(0,0,0,0.05)' },
          ticks: { padding: 15 }
        },
        x: {
          title: { display: true, text: 'UFS模块', font: { size: 14, weight: 'bold' } },
          grid: { display: false },
          ticks: { padding: 10, autoSkip: true, maxRotation: 45, minRotation: 45 }
        }
      },
      plugins: {
        title: { display: false },
        legend: { display: false },
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.7)',
          titleFont: { size: 14, weight: 'bold' },
          bodyFont: { size: 13 },
          padding: 12,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              return `问题单数量: ${context.raw}`;
            }
          }
        }
      },
      onClick: (event, elements) => {
        if (!elements.length) return;
        const clickedIndex = elements[0].index;
        const moduleName = sortedModules[clickedIndex];
        const detailList = moduleDetails[moduleName];
        if (detailList && detailList.length) {
          showDefectDetailsPage(moduleName + ' - UFS模块问题单明细', detailList);
        }
      }
    },
    plugins: [labelPlugin]
  });
}

// 新增：渲染开发领域 vs 测试领域提单总数对比图表
function renderSubmitterComparisonChart(allDefects) {
  const canvas = document.getElementById('submitterComparisonChartCanvas');
  if (!canvas) return;
  const ctx = canvas.getContext('2d');
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  if (window.submitterComparisonChartInstance) {
    window.submitterComparisonChartInstance.destroy();
  }

  // 统计开发领域和测试领域的提单数量
  let devDomainCount = 0;
  let testDomainCount = 0;
  const devDomainDetails = [];
  const testDomainDetails = [];
  
  allDefects.forEach(item => {
    if (isTestDomainDefect(item)) {
      testDomainCount++;
      testDomainDetails.push(item);
    } else {
      devDomainCount++;
      devDomainDetails.push(item);
    }
  });
  
  
  
  const data = {
    labels: ['开发领域', '测试领域'],
    datasets: [{
      label: '提单总数',
      data: [devDomainCount, testDomainCount],
      backgroundColor: ['rgba(59,130,246,0.8)', 'rgba(251,146,60,0.8)'],
      borderColor: ['rgba(59,130,246,1)', 'rgba(251,146,60,1)'],
      borderWidth: 1,
      borderRadius: 6,
      barPercentage: 0.6,
      categoryPercentage: 0.7
    }]
  };
  
  const labelPlugin = {
    id: 'customLabel',
    afterDatasetsDraw: (chart) => {
      const { ctx, data, chartArea: { top }, scales: { x, y } } = chart;
      ctx.save();
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';
      ctx.font = 'bold 12px Arial';
      data.datasets[0].data.forEach((value, index) => {
        const xCoord = x.getPixelForValue(data.labels[index]);
        let yCoord = y.getPixelForValue(value);
        if (yCoord - 15 < top) yCoord = top + 15;
        ctx.fillStyle = '#333';
        ctx.fillText(value, xCoord, yCoord - 10);
      });
      ctx.restore();
    }
  };
  
  window.submitterComparisonChartInstance = new Chart(ctx, {
    type: 'bar',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000, easing: 'easeOutQuart' },
      scales: {
        y: {
          beginAtZero: true,
          title: { display: true, text: '提单总数', font: { size: 14, weight: 'bold' } },
          grid: { color: 'rgba(0,0,0,0.05)' },
          ticks: { padding: 15 }
        },
        x: {
          title: { display: true, text: '人员类型', font: { size: 14, weight: 'bold' } },
          grid: { display: false },
          ticks: { padding: 10, autoSkip: true, maxRotation: 45, minRotation: 45 }
        }
      },
      plugins: {
        title: { display: false },
        legend: { display: false },
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.7)',
          titleFont: { size: 14, weight: 'bold' },
          bodyFont: { size: 13 },
          padding: 12,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              return `提单总数: ${context.raw}`;
            }
          }
        }
      },
      onClick: (event, elements) => {
        if (!elements.length) return;
        const clickedIndex = elements[0].index;
        const label = data.labels[clickedIndex];
        let detailList = [];
        if (label === '开发领域') {
          detailList = devDomainDetails;
        } else if (label === '测试领域') {
          detailList = testDomainDetails;
        }
        if (detailList && detailList.length) {
          showDefectDetailsPage(label + ' - 提单明细', detailList);
        }
      }
    },
    plugins: [labelPlugin]
  });
}

// 新增：渲染问题单解决率统计图表
function renderResolutionRateChart(allDefects) {
  const canvas = document.getElementById('resolutionRateChartCanvas');
  if (!canvas) return;
  const ctx = canvas.getContext('2d');
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  if (window.resolutionRateChartInstance) {
    window.resolutionRateChartInstance.destroy();
  }

  // 统计已关闭和未关闭的问题单
  let closedCount = 0;
  let openCount = 0;
  const closedDetails = [];
  const openDetails = [];
  
  allDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    
    if (state === '确认关闭') {
      closedCount++;
      closedDetails.push(item);
    } else {
      openCount++;
      openDetails.push(item);
    }
  });
  
  // 计算解决率
  const totalCount = allDefects.length;
  const resolutionRate = totalCount > 0 ? (closedCount / totalCount * 100).toFixed(1) : '0.0';
  
  
  
  // 检查是否有数据
  if (totalCount === 0) {
    const chartDom = document.getElementById('resolutionRateChartWrapper');
    if (chartDom) {
      chartDom.style.display = 'flex';
      chartDom.style.alignItems = 'center';
      chartDom.style.justifyContent = 'center';
      chartDom.style.height = '300px';
      chartDom.style.backgroundColor = '#f8f9fa';
      chartDom.style.border = '1px solid #dee2e6';
      chartDom.style.borderRadius = '8px';
      chartDom.innerHTML = '<div style="text-align: center; color: #6c757d;"><h4>暂无问题单数据</h4><p>当前没有符合条件的问题单</p></div>';
      return;
    }
  }
  
  // Chart.js 配置
  const data = {
    labels: ['已关闭', '未关闭'],
    datasets: [{
      label: '问题单数量',
      data: [closedCount, openCount],
      backgroundColor: ['rgba(34,197,94,0.8)', 'rgba(156,163,175,0.8)'],
      borderColor: ['rgba(34,197,94,1)', 'rgba(156,163,175,1)'],
      borderWidth: 2,
      cutout: '60%'
    }]
  };
  
  window.resolutionRateChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000, easing: 'easeOutQuart' },
      plugins: {
        title: { display: false },
        legend: { 
          display: true, 
          position: 'bottom',
          labels: {
            padding: 20,
            usePointStyle: true,
            font: { size: 14 }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.7)',
          titleFont: { size: 14, weight: 'bold' },
          bodyFont: { size: 13 },
          padding: 12,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              return `${context.label}: ${context.raw} 个`;
            }
          }
        }
      },
      onClick: (event, elements) => {
        if (!elements.length) return;
        const clickedIndex = elements[0].index;
        const label = data.labels[clickedIndex];
        let detailList = [];
        if (label === '已关闭') {
          detailList = closedDetails;
        } else if (label === '未关闭') {
          detailList = openDetails;
        }
        if (detailList && detailList.length) {
          showDefectDetailsPage(label + ' - 问题单明细', detailList);
        }
      }
    },
    plugins: [{
      id: 'centerText',
      afterDatasetsDraw: function(chart) {
        const { ctx, chartArea: { left, top, right, bottom } } = chart;
        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // 绘制解决率文字
        ctx.font = 'bold 24px Arial';
        ctx.fillStyle = '#1f2937';
        ctx.fillText(resolutionRate + '%', (left + right) / 2, (top + bottom) / 2 - 10);
        
        // 绘制"解决率"标签
        ctx.font = '14px Arial';
        ctx.fillStyle = '#6b7280';
        ctx.fillText('解决率', (left + right) / 2, (top + bottom) / 2 + 15);
        
        ctx.restore();
      }
    }]
  });
}

// 渲染缺陷看板页面
async function renderDefectDashboard() {
  const container = document.getElementById('defect-dashboard-content');
  if (!container) return;
  
  // 创建仪表盘布局
  container.innerHTML = `
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-primary flex items-center">
          <i class="fa-solid fa-tachometer-alt text-primary mr-2"></i>
          问题单缺陷看板
        </h2>
        <div class="flex items-center gap-2">
          <div class="text-xs text-gray-500 flex items-center">
            <i class="fa-solid fa-clock mr-1"></i>
            <span>数据更新时间：获取中...</span>
          </div>
          <button id="refresh-dashboard-data" class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors">
            <i class="fa-solid fa-sync-alt mr-1"></i>刷新数据
          </button>
        </div>
      </div>

      <!-- 数据卡片区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- 卡片1 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center mb-2">
                <i class="fa-solid fa-list text-blue-500 text-lg mr-2"></i>
                <span class="text-gray-600 text-sm" id="dashboard-card-1-title">--</span>
              </div>
              <div class="text-2xl font-bold text-gray-800" id="dashboard-card-1-value">
                --
              </div>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-cube text-blue-500 text-xl"></i>
            </div>
          </div>
        </div>

        <!-- 卡片2 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center mb-2">
                <i class="fa-solid fa-chart-bar text-red-500 text-lg mr-2"></i>
                <span class="text-gray-600 text-sm" id="dashboard-card-2-title">--</span>
              </div>
              <div class="text-2xl font-bold text-gray-800" id="dashboard-card-2-value">
                --
              </div>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-cube text-red-500 text-xl"></i>
            </div>
          </div>
        </div>

        <!-- 卡片3 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-yellow-500">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center mb-2">
                <i class="fa-solid fa-chart-line text-yellow-500 text-lg mr-2"></i>
                <span class="text-gray-600 text-sm" id="dashboard-card-3-title">--</span>
              </div>
              <div class="text-2xl font-bold text-gray-800" id="dashboard-card-3-value">
                --
              </div>
            </div>
            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-cube text-yellow-500 text-xl"></i>
            </div>
          </div>
        </div>

        <!-- 卡片4 -->
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center mb-2">
                <i class="fa-solid fa-chart-pie text-green-500 text-lg mr-2"></i>
                <span class="text-gray-600 text-sm" id="dashboard-card-4-title">--</span>
              </div>
              <div class="text-2xl font-bold text-gray-800" id="dashboard-card-4-value">
                --
              </div>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-cube text-green-500 text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="space-y-6">
        <!-- 三个图表一行并列展示 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <!-- 第一个图表：解决率统计（占2列） -->
          <div class="lg:col-span-2 bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa-solid fa-chart-pie text-blue-500 mr-2"></i>
              问题单解决率统计
            </h3>
            <div class="flex">
              <!-- 图表区域 -->
              <div class="flex-1 relative" style="height:300px;">
                <canvas id="dashboardResolutionChartCanvas" width="400" height="300"></canvas>
              </div>
              <!-- 统计数据侧边栏 -->
              <div class="w-48 ml-4 space-y-2">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                  <div class="text-sm text-gray-600 mb-1">总问题单数</div>
                  <div class="text-xl font-bold text-blue-600" id="total-defects-count">--</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                  <div class="text-sm text-gray-600 mb-1">已关闭</div>
                  <div class="text-xl font-bold text-green-600" id="closed-defects-count">--</div>
                </div>
                <div class="text-center p-3 bg-red-50 rounded-lg">
                  <div class="text-sm text-gray-600 mb-1">未关闭</div>
                  <div class="text-xl font-bold text-red-600" id="open-defects-count">--</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二个图表：未关闭DI统计饼图（占1列） -->
          <div class="lg:col-span-1 bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa-solid fa-chart-pie text-purple-500 mr-2"></i>
              未关闭DI统计
            </h3>
            <div class="space-y-3">
              <div style="width:100%;height:200px;">
                <canvas id="dashboardDiTotalChartCanvas" width="300" height="200"></canvas>
              </div>
              <!-- 数据统计 -->
              <div class="space-y-2">
                <!-- 色块和标签行 -->
                <div class="grid grid-cols-4 gap-1 text-xs mb-2">
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #F43F5E;"></div>
                    <div class="text-gray-600">致命</div>
                  </div>
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #F59E42;"></div>
                    <div class="text-gray-600">严重</div>
                  </div>
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #3B82F6;"></div>
                    <div class="text-gray-600">一般</div>
                  </div>
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #10B981;"></div>
                    <div class="text-gray-600">提示</div>
                  </div>
                </div>
                
                <!-- 个数行 -->
                <div class="flex items-center text-sm mb-1">
                  <span class="text-gray-600 mr-2">个数：</span>
                  <div class="grid grid-cols-4 gap-1 flex-1">
                    <div class="text-center font-bold" style="color: #F43F5E;" id="di-total-critical">0</div>
                    <div class="text-center font-bold" style="color: #F59E42;" id="di-total-major">0</div>
                    <div class="text-center font-bold" style="color: #3B82F6;" id="di-total-minor">0</div>
                    <div class="text-center font-bold" style="color: #10B981;" id="di-total-info">0</div>
                  </div>
                </div>
                
                <!-- DI值行 -->
                <div class="flex items-center text-sm">
                  <span class="text-gray-600 mr-2">DI值：</span>
                  <div class="grid grid-cols-4 gap-1 flex-1">
                    <div class="text-center font-bold" style="color: #F43F5E;" id="di-total-critical-value">0.0</div>
                    <div class="text-center font-bold" style="color: #F59E42;" id="di-total-major-value">0.0</div>
                    <div class="text-center font-bold" style="color: #3B82F6;" id="di-total-minor-value">0.0</div>
                    <div class="text-center font-bold" style="color: #10B981;" id="di-total-info-value">0.0</div>
                  </div>
                </div>
                <!-- 总计 -->
                <div class="border-t pt-2 grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div class="text-gray-600 text-sm">个数总和</div>
                    <div class="font-bold text-xl text-gray-800" id="di-total-sum">0</div>
                  </div>
                  <div>
                    <div class="text-gray-600 text-sm">DI值总和</div>
                    <div class="font-bold text-xl text-blue-600" id="di-total-sum-value">0.0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 第三个图表：在开发中的DI统计饼图（占1列） -->
          <div class="lg:col-span-1 bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <i class="fa-solid fa-chart-pie text-orange-500 mr-2"></i>
              在开发中的DI统计
            </h3>
            <div class="space-y-3">
              <div style="width:100%;height:200px;">
                <canvas id="dashboardDiDevChartCanvas" width="300" height="200"></canvas>
              </div>
              <!-- 数据统计 -->
              <div class="space-y-2">
                <!-- 色块和标签行 -->
                <div class="grid grid-cols-4 gap-1 text-xs mb-2">
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #F43F5E;"></div>
                    <div class="text-gray-600">致命</div>
                  </div>
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #F59E42;"></div>
                    <div class="text-gray-600">严重</div>
                  </div>
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #3B82F6;"></div>
                    <div class="text-gray-600">一般</div>
                  </div>
                  <div class="text-center">
                    <div class="w-3 h-3 rounded mx-auto mb-1" style="background-color: #10B981;"></div>
                    <div class="text-gray-600">提示</div>
                  </div>
                </div>
                
                <!-- 个数行 -->
                <div class="flex items-center text-sm mb-1">
                  <span class="text-gray-600 mr-2">个数：</span>
                  <div class="grid grid-cols-4 gap-1 flex-1">
                    <div class="text-center font-bold" style="color: #F43F5E;" id="di-dev-critical">0</div>
                    <div class="text-center font-bold" style="color: #F59E42;" id="di-dev-major">0</div>
                    <div class="text-center font-bold" style="color: #3B82F6;" id="di-dev-minor">0</div>
                    <div class="text-center font-bold" style="color: #10B981;" id="di-dev-info">0</div>
                  </div>
                </div>
                
                <!-- DI值行 -->
                <div class="flex items-center text-sm">
                  <span class="text-gray-600 mr-2">DI值：</span>
                  <div class="grid grid-cols-4 gap-1 flex-1">
                    <div class="text-center font-bold" style="color: #F43F5E;" id="di-dev-critical-value">0.0</div>
                    <div class="text-center font-bold" style="color: #F59E42;" id="di-dev-major-value">0.0</div>
                    <div class="text-center font-bold" style="color: #3B82F6;" id="di-dev-minor-value">0.0</div>
                    <div class="text-center font-bold" style="color: #10B981;" id="di-dev-info-value">0.0</div>
                  </div>
                </div>
                <!-- 总计 -->
                <div class="border-t pt-2 grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div class="text-gray-600 text-sm">个数总和</div>
                    <div class="font-bold text-xl text-gray-800" id="di-dev-sum">0</div>
                  </div>
                  <div>
                    <div class="text-gray-600 text-sm">DI值总和</div>
                    <div class="font-bold text-xl text-orange-600" id="di-dev-sum-value">0.0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // 绑定刷新按钮事件
  const refreshBtn = document.getElementById('refresh-dashboard-data');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', async () => {
      // 显示加载状态
      refreshBtn.disabled = true;
      refreshBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-1"></i>刷新中...';
      
      // 更新时间显示为刷新状态
      const timeElement = document.querySelector('#defect-dashboard-content .text-xs span');
      if (timeElement) {
        timeElement.textContent = `数据更新时间：等本次点击刷新数据...`;
      }
      
      try {
        // 刷新所有仪表盘数据和图表
        await refreshAllDashboardData();
      } catch (error) {
        console.error('刷新仪表盘数据失败:', error);
        // 刷新失败时显示错误信息
        if (timeElement) {
          timeElement.textContent = `数据更新时间：刷新失败，请重试`;
        }
      } finally {
        // 恢复按钮状态
        refreshBtn.disabled = false;
        refreshBtn.innerHTML = '<i class="fa-solid fa-sync-alt mr-1"></i>刷新数据';
      }
    });
  }

  // 初次加载时获取并显示数据
  await refreshAllDashboardData();
}

// 统一刷新所有仪表盘数据和图表
async function refreshAllDashboardData() {
  try {
    // 获取缺陷数据（会返回数据获取时间信息）
    const dashboardData = await fetchDefectDataForDashboard();
    
    // 更新数据更新时间
    const timeElement = document.querySelector('#defect-dashboard-content .text-xs span');
    if (timeElement && dashboardData) {
      if (dashboardData.dataUpdateTime) {
        timeElement.textContent = `数据更新时间：${dashboardData.dataUpdateTime}`;
      } else {
        timeElement.textContent = `数据更新时间：${new Date().toLocaleString()}`;
      }
    }
    
    // 更新数据卡片
    if (dashboardData) {
      updateDashboardCards(dashboardData);
    }
    
    // 刷新所有图表（后续添加图表时在这里调用对应的渲染函数）
    await refreshAllDashboardCharts(dashboardData);
  } catch (error) {
    console.error('刷新仪表盘数据失败:', error);
    // 如果获取数据失败，显示当前时间
    const timeElement = document.querySelector('#defect-dashboard-content .text-xs span');
    if (timeElement) {
      timeElement.textContent = `数据更新时间：${new Date().toLocaleString()}（获取失败）`;
    }
  }
}

// 获取缺陷数据并渲染仪表盘图表（使用与子菜单页面完全相同的数据获取逻辑）
async function fetchDefectDataForDashboard() {
  try {
    // 记录数据获取开始时间
    const fetchStartTime = new Date().toLocaleString();
    
    // 使用与子菜单页面完全相同的数据获取逻辑
    // 拉取所有缺陷数据
    let allDefects = [];
    let page = 1;
    let pageSize = 100;
    let total = 0;
    
    do {
      const res = await fetch('/api/pingcode/defects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ page, size: pageSize })
      });
      if (!res.ok) break;
      const data = await res.json();
      const values = data.values || [];
      total = data.total || 0;
      allDefects = allDefects.concat(values);
      page++;
    } while (allDefects.length < total);

    // 获取流转历史数据（与子菜单页面完全相同）
    let defectFlowAnalysis = {};
    try {
      const defectIds = allDefects.map(defect => defect.id);
      const flowRes = await fetch('/api/pingcode/defect-flows', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ defectIds })
      });
      
      if (flowRes.ok) {
        const flowData = await flowRes.json();
        if (flowData.success && flowData.results) {
          defectFlowAnalysis = flowData.results;
          window.defectFlowAnalysis = defectFlowAnalysis;
        }
      }
    } catch (error) {
      console.warn('获取问题单流转历史失败:', error);
    }
    
    // 数据获取完成时间
    const dataUpdateTime = new Date().toLocaleString();

    return {
      allDefects,
      defectFlowAnalysis,
      dataUpdateTime: dataUpdateTime
    };
  } catch (error) {
    console.error('获取缺陷数据失败:', error);
    // 返回空数据作为后备
    return {
      allDefects: [],
      defectFlowAnalysis: {},
      dataUpdateTime: new Date().toLocaleString()
    };
  }
}

// 刷新所有仪表盘图表（使用与子菜单页面完全相同的渲染逻辑）
async function refreshAllDashboardCharts(data) {
  if (!data || !data.allDefects) return;
  
  try {
    // 渲染三个主要图表（使用与子菜单页面完全相同的函数调用）
    await renderDashboardResolutionChart(data.allDefects);
    await renderDashboardDiTotalPieChart(data.allDefects, data.defectFlowAnalysis);
    await renderDashboardDiDevPieChart(data.allDefects, data.defectFlowAnalysis);
    
    console.log('仪表盘图表刷新完成，数据量:', data.allDefects.length);
  } catch (error) {
    console.error('刷新仪表盘图表失败:', error);
  }
}

// 仪表盘版本的解决率统计图表（完全复用原有逻辑）
let dashboardResolutionChartInstance = null;
async function renderDashboardResolutionChart(allDefects) {
  if (!allDefects || allDefects.length === 0) return;
  
  // 使用与原解决率统计页面完全相同的逻辑
  let closedCount = 0;
  let openCount = 0;
  const closedDetails = [];
  const openDetails = [];
  
  allDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    
    if (state === '确认关闭') {
      closedCount++;
      closedDetails.push(item);
    } else {
      openCount++;
      openDetails.push(item);
    }
  });
  
  // 计算解决率
  const totalCount = allDefects.length;
  const resolutionRate = totalCount > 0 ? (closedCount / totalCount * 100).toFixed(1) : '0.0';
  
  // 更新右侧统计数字
  document.getElementById('total-defects-count').textContent = totalCount;
  document.getElementById('closed-defects-count').textContent = closedCount;
  document.getElementById('open-defects-count').textContent = openCount;
  
  // 渲染解决率图表（使用与原页面相同的甜甜圈图）
  const canvas = document.getElementById('dashboardResolutionChartCanvas');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  if (dashboardResolutionChartInstance) {
    dashboardResolutionChartInstance.destroy();
  }
  
  // 使用与原页面相同的甜甜圈图配置
  const data = {
    labels: ['已关闭', '未关闭'],
    datasets: [{
      label: '问题单数量',
      data: [closedCount, openCount],
      backgroundColor: ['rgba(34,197,94,0.8)', 'rgba(156,163,175,0.8)'],
      borderColor: ['rgba(34,197,94,1)', 'rgba(156,163,175,1)'],
      borderWidth: 2,
      cutout: '60%'
    }]
  };
  
  dashboardResolutionChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      animation: { duration: 1000, easing: 'easeOutQuart' },
      plugins: {
        title: { display: false },
        legend: { 
          display: true, 
          position: 'bottom',
          labels: {
            padding: 20,
            usePointStyle: true,
            font: { size: 14 },
            generateLabels: function(chart) {
              const data = chart.data;
              if (data.labels.length && data.datasets.length) {
                return data.labels.map((label, i) => {
                  const count = data.datasets[0].data[i];
                  const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : '0.0';
                  return {
                    text: `${label}: ${count} (${percentage}%)`,
                    fillStyle: data.datasets[0].backgroundColor[i],
                    strokeStyle: data.datasets[0].borderColor[i],
                    lineWidth: data.datasets[0].borderWidth,
                    pointStyle: 'circle',
                    hidden: false,
                    index: i
                  };
                });
              }
              return [];
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.7)',
          titleFont: { size: 14, weight: 'bold' },
          bodyFont: { size: 13 },
          padding: 12,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              const count = context.parsed;
              const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : '0.0';
              return `${context.label}: ${count} 个`;
            }
          }
        }
      },
      onClick: function(evt, elements) {
        if (elements.length > 0) {
          const element = elements[0];
          const label = data.labels[element.index];
          const details = element.index === 0 ? closedDetails : openDetails;
          // 使用与子菜单页面相同的明细显示功能
          showDefectDetailsPage(`解决率统计 - ${label}`, details);
        }
      }
    },
    plugins: [{
      id: 'centerText',
      afterDatasetsDraw: function(chart) {
        const { ctx, chartArea: { left, top, right, bottom } } = chart;
        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // 绘制解决率文字
        ctx.font = 'bold 24px Arial';
        ctx.fillStyle = '#1f2937';
        ctx.fillText(resolutionRate + '%', (left + right) / 2, (top + bottom) / 2 - 10);
        
        // 绘制"解决率"标签
        ctx.font = '14px Arial';
        ctx.fillStyle = '#6b7280';
        ctx.fillText('解决率', (left + right) / 2, (top + bottom) / 2 + 15);
        
        ctx.restore();
      }
    }]
  });
}

// 仪表盘版本的未关闭DI统计饼图（完全复用原有逻辑）
let dashboardDiTotalChartInstance = null;
async function renderDashboardDiTotalPieChart(allDefects, defectFlowAnalysis) {
  if (!allDefects || allDefects.length === 0) return;
  
  // 使用与原DI统计页面完全相同的逻辑
  // DI分值映射
  const severityScore = {
    "致命": 10,
    "严重": 3,
    "一般": 1,
    "提示": 0.1
  };
  const statusClosed = '确认关闭';
  const severityLabels = ['致命', '严重', '一般', '提示'];
  
  // 统计未关闭（与原逻辑完全一致）
  const diTotal = { "致命": 0, "严重": 0, "一般": 0, "提示": 0 };
  const diTotalDetail = { "致命": [], "严重": [], "一般": [], "提示": [] };
  const diTotalCount = { "致命": 0, "严重": 0, "一般": 0, "提示": 0 };
  
  allDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    if (state === statusClosed) return;
    const p = item.properties || {};
    const severity = DICT.severity[p.severity] || '';
    if (severityScore[severity]) {
      diTotal[severity] += severityScore[severity];
      diTotalDetail[severity].push(item);
      diTotalCount[severity]++; // 计数而不是DI分值
    }
  });
  
  // 更新统计数字（显示问题单数量和DI值）
          document.getElementById('di-total-critical').textContent = diTotalCount['致命'];
        document.getElementById('di-total-critical-value').textContent = diTotal['致命'].toFixed(1);

        document.getElementById('di-total-major').textContent = diTotalCount['严重'];
        document.getElementById('di-total-major-value').textContent = diTotal['严重'].toFixed(1);

        document.getElementById('di-total-minor').textContent = diTotalCount['一般'];
        document.getElementById('di-total-minor-value').textContent = diTotal['一般'].toFixed(1);

        document.getElementById('di-total-info').textContent = diTotalCount['提示'];
        document.getElementById('di-total-info-value').textContent = diTotal['提示'].toFixed(1);
  
  const totalCount = diTotalCount['致命'] + diTotalCount['严重'] + diTotalCount['一般'] + diTotalCount['提示'];
  const totalDI = diTotal['致命'] + diTotal['严重'] + diTotal['一般'] + diTotal['提示'];
  document.getElementById('di-total-sum').textContent = totalCount;
  document.getElementById('di-total-sum-value').textContent = totalDI.toFixed(1);
  
  // 渲染饼图
  const canvas = document.getElementById('dashboardDiTotalChartCanvas');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  if (dashboardDiTotalChartInstance) {
    dashboardDiTotalChartInstance.destroy();
  }
  
  // 准备饼图数据（只包含有数据的部分）
  const data = [];
  const labels = [];
  const colors = [];
  const details = [];
  
  if (diTotalCount['致命'] > 0) {
    data.push(diTotalCount['致命']);
    labels.push('致命');
    colors.push('#F43F5E');
    details.push(diTotalDetail['致命']);
  }
  if (diTotalCount['严重'] > 0) {
    data.push(diTotalCount['严重']);
    labels.push('严重');
    colors.push('#F59E42');
    details.push(diTotalDetail['严重']);
  }
  if (diTotalCount['一般'] > 0) {
    data.push(diTotalCount['一般']);
    labels.push('一般');
    colors.push('#3B82F6');
    details.push(diTotalDetail['一般']);
  }
  if (diTotalCount['提示'] > 0) {
    data.push(diTotalCount['提示']);
    labels.push('提示');
    colors.push('#10B981');
    details.push(diTotalDetail['提示']);
  }
  
  dashboardDiTotalChartInstance = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: colors,
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const count = context.parsed;
              const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : '0.0';
              const severity = labels[context.dataIndex];
              const diValue = diTotal[severity].toFixed(1);
              return `${context.label}: ${count}个 (${percentage}%) DI: ${diValue}`;
            }
          }
        }
      },
      onClick: function(evt, elements) {
        if (elements.length > 0) {
          const element = elements[0];
          const label = labels[element.index];
          const detailList = details[element.index];
          // 显示明细（与原页面相同的功能）
          showDefectDetailsPage('未关闭DI-' + label, detailList);
        }
      }
    }
  });
}

// 仪表盘版本的在开发中DI统计饼图（完全复用原有逻辑）
let dashboardDiDevChartInstance = null;
async function renderDashboardDiDevPieChart(allDefects, defectFlowAnalysis) {
  if (!allDefects || allDefects.length === 0) return;
  
  // 使用与原DI统计页面完全相同的逻辑
  // DI分值映射
  const severityScore = {
    "致命": 10,
    "严重": 3,
    "一般": 1,
    "提示": 0.1
  };
  const statusClosed = '确认关闭';
  const severityLabels = ['致命', '严重', '一般', '提示'];
  
  // 统计开发环节（与原逻辑完全一致）
  const diDev = { "致命": 0, "严重": 0, "一般": 0, "提示": 0 };
  const diDevDetail = { "致命": [], "严重": [], "一般": [], "提示": [] };
  const diDevCount = { "致命": 0, "严重": 0, "一般": 0, "提示": 0 };
  let devCount = 0;
  
  allDefects.forEach(item => {
    const state = item.state ? item.state.name : '';
    if (state === statusClosed) return;
    
    // 只统计开发领域的问题单（非测试领域）
    const flowHistory = defectFlowAnalysis[item.id];
    if (!flowHistory || !flowHistory.transitions) return;
    
    // 判断是否在开发环节（提交审核后有下一个节点）
    let found = false;
    
    // 检查flowHistory.transitions是否存在
    if (!flowHistory.transitions) {
      return;
    }
    
    for (let i = 0; i < flowHistory.transitions.length; i++) {
      const transition = flowHistory.transitions[i];
      
      // 检查to_state.name字段是否包含"提交审核"
      if (transition.to_state && transition.to_state.name && transition.to_state.name.includes('提交审核')) {
        if (flowHistory.transitions[i + 1]) {
          found = true;
        }
        break;
      }
    }
    
    if (!found) return;
    devCount++;
    const p = item.properties || {};
    const severity = DICT.severity[p.severity] || '';
    if (severityScore[severity]) {
      diDev[severity] += severityScore[severity];
      diDevDetail[severity].push(item);
      diDevCount[severity]++; // 计数而不是DI分值
    }
  });
  
  // 更新统计数字（显示问题单数量和DI值）
          document.getElementById('di-dev-critical').textContent = diDevCount['致命'];
        document.getElementById('di-dev-critical-value').textContent = diDev['致命'].toFixed(1);

        document.getElementById('di-dev-major').textContent = diDevCount['严重'];
        document.getElementById('di-dev-major-value').textContent = diDev['严重'].toFixed(1);

        document.getElementById('di-dev-minor').textContent = diDevCount['一般'];
        document.getElementById('di-dev-minor-value').textContent = diDev['一般'].toFixed(1);

        document.getElementById('di-dev-info').textContent = diDevCount['提示'];
        document.getElementById('di-dev-info-value').textContent = diDev['提示'].toFixed(1);
  
  const totalCount = diDevCount['致命'] + diDevCount['严重'] + diDevCount['一般'] + diDevCount['提示'];
  const totalDI = diDev['致命'] + diDev['严重'] + diDev['一般'] + diDev['提示'];
  document.getElementById('di-dev-sum').textContent = totalCount;
  document.getElementById('di-dev-sum-value').textContent = totalDI.toFixed(1);
  
  // 渲染饼图
  const canvas = document.getElementById('dashboardDiDevChartCanvas');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  if (dashboardDiDevChartInstance) {
    dashboardDiDevChartInstance.destroy();
  }
  
  // 准备饼图数据（只包含有数据的部分）
  const data = [];
  const labels = [];
  const colors = [];
  const details = [];
  
  if (diDevCount['致命'] > 0) {
    data.push(diDevCount['致命']);
    labels.push('致命');
    colors.push('#F43F5E'); // 致命使用红色，与右侧标识一致
    details.push(diDevDetail['致命']);
  }
  if (diDevCount['严重'] > 0) {
    data.push(diDevCount['严重']);
    labels.push('严重');
    colors.push('#F59E42');
    details.push(diDevDetail['严重']);
  }
  if (diDevCount['一般'] > 0) {
    data.push(diDevCount['一般']);
    labels.push('一般');
    colors.push('#3B82F6');
    details.push(diDevDetail['一般']);
  }
  if (diDevCount['提示'] > 0) {
    data.push(diDevCount['提示']);
    labels.push('提示');
    colors.push('#10B981');
    details.push(diDevDetail['提示']);
  }
  
  dashboardDiDevChartInstance = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: colors,
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const count = context.parsed;
              const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : '0.0';
              const severity = labels[context.dataIndex];
              const diValue = diDev[severity].toFixed(1);
              return `${context.label}: ${count}个 (${percentage}%) DI: ${diValue}`;
            }
          }
        }
      },
      onClick: function(evt, elements) {
        if (elements.length > 0) {
          const element = elements[0];
          const label = labels[element.index];
          const detailList = details[element.index];
          // 显示明细（与原页面相同的功能）
          showDefectDetailsPage('开发环节DI-' + label, detailList);
        }
      }
    }
  });
}

// 更新仪表盘数据卡片
function updateDashboardCards(dashboardData) {
  if (!dashboardData || !dashboardData.allDefects || !dashboardData.defectFlowAnalysis) {
    return;
  }

  // 计算平均定位时间（使用与子菜单栏完全相同的逻辑）
  const avgTimeData = calculateAverageLocationTime(dashboardData.allDefects, dashboardData.defectFlowAnalysis);
  
  // 更新第一个小卡片显示平均定位时间
  const card1Title = document.getElementById('dashboard-card-1-title');
  const card1Value = document.getElementById('dashboard-card-1-value');
  const card1Container = document.querySelector('#defect-dashboard-content .grid .bg-white:first-child');
  
  if (card1Title && card1Value && card1Container) {
    card1Title.textContent = '平均定位时间';
    card1Value.textContent = avgTimeData.averageTime + ' 天';
    
    // 添加点击事件显示明细
    card1Container.style.cursor = 'pointer';
    card1Container.onclick = () => {
      if (avgTimeData.allLocationDetails.length > 0) {
        showDefectDetailsPage('问题单的平均定位时间明细', avgTimeData.allLocationDetails);
      }
    };
    
    // 更新图标
    const card1Icon = card1Container.querySelector('.fa-cube');
    if (card1Icon) {
      card1Icon.className = 'fa-solid fa-clock text-blue-500 text-xl';
    }
  }
}

// 计算平均定位时间（与子菜单栏逻辑完全一致）
function calculateAverageLocationTime(allDefects, defectFlowAnalysis) {
  // 只统计测试领域的问题单（与原逻辑完全一致）
  const testDomainDefects = allDefects.filter(item => isTestDomainDefect(item));
  
  // 统计所有问题单的定位时间
  let totalLocationTime = 0;  // 所有问题单定位时间总和
  let validDefectCount = 0;   // 有效问题单数量
  const allLocationDetails = [];  // 所有定位时间详情
  
  testDomainDefects.forEach(item => {
    const flowHistory = defectFlowAnalysis[item.id];
    if (!flowHistory || !flowHistory.transitions) return;
    
    let firstDevTime = null;  // 第一次进入开发环节的时间
    let lastRegressionTime = null;  // 最后一次进入回归测试的时间
    
    // 分析流转历史（与原逻辑完全一致）
    flowHistory.transitions.forEach((transition, index) => {
      const toState = transition.to_state ? transition.to_state.name : '';
      const timestamp = transition.created_at || 0;
      
      // 识别第一次进入开发环节（提交审核后的下一个流转）
      if (toState.includes('提交审核')) {
        // 找到提交审核，下一个流转就是进入开发环节
        if (flowHistory.transitions[index + 1]) {
          const nextTransition = flowHistory.transitions[index + 1];
          firstDevTime = nextTransition.created_at * 1000;
        }
      }
      
      // 识别最后一次进入回归测试
      if (toState.includes('回归测试')) {
        lastRegressionTime = timestamp * 1000;
      }
    });
    
    // 如果找到了有效的定位时间
    if (firstDevTime && lastRegressionTime && lastRegressionTime > firstDevTime) {
      const locationTime = (lastRegressionTime - firstDevTime) / (1000 * 60 * 60 * 24); // 转换为天
      
      totalLocationTime += locationTime;
      validDefectCount++;
      
      allLocationDetails.push({
        ...item,
        locationTime: locationTime,
        firstDevTime: firstDevTime,
        lastRegressionTime: lastRegressionTime
      });
    }
  });
  
  // 计算整体平均定位时间
  const averageLocationTime = validDefectCount > 0 ? totalLocationTime / validDefectCount : 0;
  
  return {
    averageTime: averageLocationTime.toFixed(2),
    validDefectCount: validDefectCount,
    allLocationDetails: allLocationDetails
  };
}

// ============== 以下为将来扩展其他子菜单图表的预留区域 ==============

// 预留函数：渲染其他子菜单图表到仪表盘
async function renderOtherDashboardCharts(allDefects, defectFlowAnalysis) {
  // 将来可以在这里添加其他子菜单图表的仪表盘版本
  // 例如：
  // await renderDashboardLocationOverdueChart(allDefects, defectFlowAnalysis);  // 定位超期统计
  // await renderDashboardTrendChart(allDefects, defectFlowAnalysis);            // 趋势分析  
  // await renderDashboardAvgTimeChart(allDefects, defectFlowAnalysis);          // 平均定位时间
  // await renderDashboardModuleChart(allDefects);                               // 模块统计
  // await renderDashboardSubmitCompareChart(allDefects);                        // 提单对比
  
  console.log('其他图表渲染预留位置');
}

// 预留函数：为将来的图表添加HTML容器
function addDashboardChartContainer(chartType, chartTitle, chartId) {
  // 动态添加图表容器的通用函数
  // 将来可以用来扩展更多图表
  console.log(`预留图表容器添加功能: ${chartType} - ${chartTitle}`);
}







// 仪表盘版本的解决率统计图表
function renderDashboardResolutionRateChart(allDefects) {
  // 已清空，不执行任何操作
  // 仪表盘解决率统计图表功能已清空
}

// 仪表盘版本的平均定位时间统计图表
function renderDashboardAverageLocationTimeChart(allDefects, defectFlowAnalysis) {
  // 已清空，不执行任何操作
  // 仪表盘平均定位时间统计图表功能已清空
}

// 仪表盘版本的DI统计图表
function renderDashboardDIBarCharts(allDefects, defectFlowAnalysis) {
  // 已清空，不执行任何操作
  // 仪表盘DI统计图表功能已清空
}

// 仪表盘版本的提单对比图表
function renderDashboardSubmitterComparisonChart(allDefects) {
  // 已清空，不执行任何操作
  // 仪表盘提单对比图表功能已清空
}

// 获取计划关闭时间的本地存储key
function getPlanClosedDateStorageKey() {
  return 'pingcode_plan_closed_dates';
}

// 从本地存储获取计划关闭时间数据
function getPlanClosedDatesFromStorage() {
  try {
    const data = localStorage.getItem(getPlanClosedDateStorageKey());
    const parsedData = data ? JSON.parse(data) : {};
    
    // 添加数据完整性检查
    if (typeof parsedData === 'object' && parsedData !== null) {
      return parsedData;
    } else {
      console.warn('本地存储数据格式异常，重置为空对象');
      return {};
    }
  } catch (error) {
    console.error('读取本地存储失败:', error);
    // 尝试恢复备份数据
    return getBackupPlanClosedDates();
  }
}

// 备份数据的键名
function getBackupStorageKey() {
  return 'pingcode_plan_closed_dates_backup';
}

// 获取备份数据
function getBackupPlanClosedDates() {
  try {
    const backupData = localStorage.getItem(getBackupStorageKey());
    return backupData ? JSON.parse(backupData) : {};
  } catch (error) {
    console.error('读取备份数据失败:', error);
    return {};
  }
}

// 创建数据备份
function createBackup(data) {
  try {
    localStorage.setItem(getBackupStorageKey(), JSON.stringify(data));
    console.log('已创建数据备份');
  } catch (error) {
    console.error('创建备份失败:', error);
  }
}

// 保存计划关闭时间到本地存储
function savePlanClosedDateToStorage(identifier, date) {
  try {
    const data = getPlanClosedDatesFromStorage();
    if (date) {
      data[identifier] = date;
    } else {
      delete data[identifier];
    }
    
    // 保存主数据
    localStorage.setItem(getPlanClosedDateStorageKey(), JSON.stringify(data));
    
    // 创建备份（每次保存时都备份）
    createBackup(data);
    
    // 添加时间戳记录最后修改时间
    localStorage.setItem('pingcode_last_modified', new Date().toISOString());
    
    console.log(`已保存数据并备份，当前共有 ${Object.keys(data).length} 条计划关闭时间记录`);
  } catch (error) {
    console.error('保存到本地存储失败:', error);
  }
}

// 更新计划关闭日期
async function updatePlanClosedDate(identifier, newDate) {
  try {
    // 保存到本地存储
    savePlanClosedDateToStorage(identifier, newDate);
    console.log(`已保存问题单 ${identifier} 的计划关闭时间: ${newDate}`);
    
    // 更新当前行的样式
    const input = document.querySelector(`input[onchange*="${identifier}"]`);
    if (input) {
      const isOverdue = newDate && new Date(newDate) < new Date();
      const row = input.closest('tr');
      
      // 更新行样式
      if (isOverdue) {
        row.classList.add('bg-red-50');
        input.classList.add('overdue');
      } else {
        row.classList.remove('bg-red-50');
        input.classList.remove('overdue');
      }
    }
    
    // 可以在这里添加API调用
    /*
    const response = await fetch('/api/pingcode/update-plan-closed-date', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: identifier,
        planClosedDate: newDate
      })
    });
    
    if (response.ok) {
      console.log('已同步到服务器');
    }
    */
  } catch (error) {
    console.error('更新计划关闭时间失败:', error);
  }
}

// 导出数据到文件（用户手动备份）
function exportPlanClosedDates() {
  try {
    const data = getPlanClosedDatesFromStorage();
    const exportData = {
      data: data,
      exportTime: new Date().toISOString(),
      version: '1.0',
      count: Object.keys(data).length
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pingcode_plan_dates_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log(`已导出 ${Object.keys(data).length} 条计划关闭时间数据`);
  } catch (error) {
    console.error('导出数据失败:', error);
  }
}

// 从文件导入数据（用户手动恢复）
function importPlanClosedDates(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const importData = JSON.parse(e.target.result);
        if (importData.data && typeof importData.data === 'object') {
          // 合并现有数据和导入数据
          const currentData = getPlanClosedDatesFromStorage();
          const mergedData = { ...currentData, ...importData.data };
          
          localStorage.setItem(getPlanClosedDateStorageKey(), JSON.stringify(mergedData));
          createBackup(mergedData);
          
          console.log(`已导入 ${Object.keys(importData.data).length} 条数据，总共 ${Object.keys(mergedData).length} 条记录`);
          resolve(mergedData);
        } else {
          reject(new Error('导入文件格式不正确'));
        }
      } catch (error) {
        reject(error);
      }
    };
    reader.readAsText(file);
  });
}

// 检查数据完整性和统计信息
function getStorageInfo() {
  const data = getPlanClosedDatesFromStorage();
  const backup = getBackupPlanClosedDates();
  const lastModified = localStorage.getItem('pingcode_last_modified');
  
  return {
    mainDataCount: Object.keys(data).length,
    backupDataCount: Object.keys(backup).length,
    lastModified: lastModified ? new Date(lastModified) : null,
    storageSize: JSON.stringify(data).length,
    isBackupConsistent: JSON.stringify(data) === JSON.stringify(backup)
  };
}

// 将函数设为全局，以便HTML中的onchange事件可以调用
window.updatePlanClosedDate = updatePlanClosedDate;
window.exportPlanClosedDates = exportPlanClosedDates;
window.importPlanClosedDates = importPlanClosedDates;
window.getStorageInfo = getStorageInfo;

// ====== 问题单评论功能 ======

// 显示问题单评论弹窗
async function showDefectCommentModal(identifier, title) {
  // 移除已存在的弹窗
  document.getElementById('defectCommentModal')?.remove();
  document.getElementById('defectCommentMask')?.remove();
  
  // 创建遮罩层
  const mask = document.createElement('div');
  mask.id = 'defectCommentMask';
  mask.className = 'fixed inset-0 bg-black bg-opacity-20 z-40';
  mask.onclick = closeDefectCommentModal;
  
  // 创建弹窗
  const modal = document.createElement('div');
  modal.id = 'defectCommentModal';
  modal.className = 'fixed inset-0 z-50 flex items-center justify-center';
  modal.innerHTML = `
    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto p-8 relative">
      <button id="defectCommentClose" class="absolute top-4 right-4 text-gray-400 hover:text-gray-700 text-2xl">&times;</button>
      <div id="defectCommentContent"></div>
    </div>
  `;
  
  // 插入到页面
  document.body.appendChild(mask);
  document.body.appendChild(modal);
  
  // 绑定关闭事件
  document.getElementById('defectCommentClose').onclick = closeDefectCommentModal;
  
  // 填充弹窗内容
  const modalHtml = `
    <div style='font-size:1.5rem;font-weight:bold;margin-bottom:18px;'>
      🐛 ${identifier} - ${title}
    </div>
    <div style='display:flex;flex-direction:column;height:420px;min-height:320px;'>
      <div id='defect-comments-list-${identifier}' style='flex:1;overflow-y:auto;border:1px solid #e5e7eb;border-radius:8px;padding:16px;margin-bottom:16px;'></div>
      <div style='display:flex;align-items:flex-end;gap:10px;'>
        <div id='defect-comment-avatar'></div>
        <textarea id='defect-comment-input-${identifier}' 
                  placeholder='输入您的评论...' 
                  style='flex:1;border:1px solid #ddd;border-radius:6px;padding:8px 12px;font-size:15px;min-height:48px;resize:vertical;'></textarea>
        <button onclick='submitDefectComment("${identifier}")' 
                style='background:#2563eb;color:#fff;border:none;border-radius:6px;padding:8px 18px;font-size:15px;cursor:pointer;'>
          发表评论
        </button>
      </div>
    </div>
  `;
  
  document.getElementById('defectCommentContent').innerHTML = modalHtml;
  
  // 渲染当前用户头像
  renderDefectCommentAvatar();
  
  // 加载评论列表
  await loadDefectComments(identifier);
  
  // 绑定回车提交
  const input = document.getElementById(`defect-comment-input-${identifier}`);
  if (input) {
    input.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        submitDefectComment(identifier);
      }
    });
    input.focus();
  }
}

// 关闭评论弹窗
function closeDefectCommentModal() {
  document.getElementById('defectCommentModal')?.remove();
  document.getElementById('defectCommentMask')?.remove();
}

// 加载问题单评论
async function loadDefectComments(identifier) {
  const listDiv = document.getElementById(`defect-comments-list-${identifier}`);
  if (!listDiv) return;
  
  listDiv.innerHTML = '<div style="color:#999;text-align:center;padding:20px;">加载中...</div>';
  
  try {
    const res = await fetch(`/api/defects/${identifier}/comments`);
    if (!res.ok) throw new Error('加载失败');
    
    const comments = await res.json();
    
    if (Array.isArray(comments) && comments.length > 0) {
      // 获取当前用户信息
      const userRes = await fetch('/api/username');
      const userInfo = userRes.ok ? await userRes.json() : { username: '', name: '' };
      
      // 渲染评论列表
      listDiv.innerHTML = comments.map(c => renderDefectCommentItem(c, userInfo)).join('');
      
      // 更新表格中的评论预览
      updateCommentPreview(identifier, comments[0], comments.length);
    } else {
      listDiv.innerHTML = '<div style="color:#999;text-align:center;padding:20px;">暂无评论，来发表第一条评论吧！</div>';
      updateCommentPreview(identifier, null, 0);
    }
  } catch (e) {
    listDiv.innerHTML = '<div style="color:#f00;text-align:center;padding:20px;">评论加载失败</div>';
    updateCommentPreview(identifier, null, 0);
    console.error('加载问题单评论失败:', e);
  }
}

// 渲染单条评论
function renderDefectCommentItem(comment, userInfo) {
  const isSelf = comment.comment_by_name === userInfo.name;
  const avatar = `<div style="width:32px;height:32px;border-radius:50%;background:#3b82f6;color:white;display:flex;align-items:center;justify-content:center;font-size:14px;font-weight:bold;">${comment.comment_by_name.charAt(0)}</div>`;
  
  return `
    <div style='display:flex;align-items:flex-start;gap:12px;margin-bottom:16px;'>
      ${avatar}
      <div style='flex:1;'>
        <div style='display:flex;justify-content:space-between;align-items:center;margin-bottom:4px;'>
          <div style='font-weight:bold;font-size:15px;color:#374151;'>
            ${comment.comment_by_name}
            <span style='color:#6b7280;font-size:13px;font-weight:normal;margin-left:8px;'>
              ${formatCommentTime(comment.comment_at)}
            </span>
          </div>
          ${isSelf ? `<button class="defect-comment-delete text-red-500 hover:text-red-700 text-sm px-2 py-1 rounded" data-id="${comment.id}" data-identifier="${comment.defect_identifier}">删除</button>` : ''}
        </div>
        <div style='color:#374151;white-space:pre-line;word-break:break-word;line-height:1.5;'>
          ${comment.comment_text}
        </div>
      </div>
    </div>
  `;
}

// 提交评论
async function submitDefectComment(identifier) {
  const input = document.getElementById(`defect-comment-input-${identifier}`);
  if (!input) return;
  
  const text = input.value.trim();
  if (!text) {
    alert('请输入评论内容');
    return;
  }
  
  try {
    const res = await fetch(`/api/defects/${identifier}/comments`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ comment_text: text })
    });
    
    if (!res.ok) throw new Error('评论失败');
    
    input.value = '';
    await loadDefectComments(identifier);
    
    // 滚动到底部
    const listDiv = document.getElementById(`defect-comments-list-${identifier}`);
    if (listDiv) listDiv.scrollTop = listDiv.scrollHeight;
    
  } catch (e) {
    alert('评论失败: ' + (e.message || '未知错误'));
    console.error('提交评论失败:', e);
  }
}

// 更新表格中的评论预览
function updateCommentPreview(identifier, latestComment, commentCount = 0) {
  const previewEl = document.getElementById(`comment-preview-${identifier}`);
  if (!previewEl) return;
  
  if (latestComment) {
    const time = new Date(latestComment.comment_at).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
    
    const fullCommentText = latestComment.comment_text;
    
    previewEl.innerHTML = `
      <div class="text-xs font-medium text-blue-600 mb-1">${latestComment.comment_by_name} ${time} (${commentCount}条)</div>
      <div class="text-xs text-gray-600 leading-normal comment-text-content" 
           data-full-text="${fullCommentText.replace(/"/g, '&quot;')}"
           onmouseover="showCommentTooltip(this, event)" 
           onmouseout="hideCommentTooltip()"
           onmousemove="moveCommentTooltip(event)">${fullCommentText}</div>
    `;
  } else {
    previewEl.innerHTML = '💬 暂无评论';
  }
}

// 显示评论悬停提示
function showCommentTooltip(element, event) {
  const fullText = element.getAttribute('data-full-text');
  const currentText = element.textContent;
  
  // 只有当文本被截断时才显示tooltip
  if (element.scrollHeight > element.clientHeight || element.scrollWidth > element.clientWidth) {
    let tooltip = document.getElementById('comment-tooltip');
    if (!tooltip) {
      tooltip = document.createElement('div');
      tooltip.id = 'comment-tooltip';
      tooltip.style.cssText = `
        position: fixed;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        max-width: 400px;
        word-wrap: break-word;
        white-space: normal;
        z-index: 10000;
        pointer-events: none;
        line-height: 1.4;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      `;
      document.body.appendChild(tooltip);
    }
    
    tooltip.textContent = fullText;
    tooltip.style.display = 'block';
    moveCommentTooltip(event);
  }
}

// 隐藏评论悬停提示
function hideCommentTooltip() {
  const tooltip = document.getElementById('comment-tooltip');
  if (tooltip) {
    tooltip.style.display = 'none';
  }
}

// 移动评论悬停提示
function moveCommentTooltip(event) {
  const tooltip = document.getElementById('comment-tooltip');
  if (tooltip && tooltip.style.display !== 'none') {
    const x = event.clientX + 10;
    const y = event.clientY + 10;
    
    // 防止tooltip超出屏幕
    const maxX = window.innerWidth - tooltip.offsetWidth - 20;
    const maxY = window.innerHeight - tooltip.offsetHeight - 20;
    
    tooltip.style.left = Math.min(x, maxX) + 'px';
    tooltip.style.top = Math.min(y, maxY) + 'px';
  }
}

// 渲染当前用户头像
function renderDefectCommentAvatar() {
  fetch('/api/username')
    .then(res => res.json())
    .then(user => {
      const avatarEl = document.getElementById('defect-comment-avatar');
      if (avatarEl) {
        avatarEl.innerHTML = `<div style="width:32px;height:32px;border-radius:50%;background:#10b981;color:white;display:flex;align-items:center;justify-content:center;font-size:14px;font-weight:bold;">${user.name.charAt(0)}</div>`;
      }
    })
    .catch(e => console.error('获取用户信息失败:', e));
}

// 格式化评论时间
function formatCommentTime(dateStr) {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) return dateStr;
  
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 删除评论事件绑定
document.addEventListener('click', async function(e) {
  if (e.target.classList.contains('defect-comment-delete')) {
    const commentId = e.target.dataset.id;
    const identifier = e.target.dataset.identifier;
    
    if (!confirm('确定要删除这条评论吗？')) return;
    
    try {
      const res = await fetch(`/api/defects/${identifier}/comments/${commentId}`, {
        method: 'DELETE'
      });
      
      if (!res.ok) throw new Error('删除失败');
      
      await loadDefectComments(identifier);
    } catch (e) {
      alert('删除失败: ' + (e.message || '未知错误'));
      console.error('删除评论失败:', e);
    }
  }
});

// 批量加载评论预览
async function loadCommentPreviewsForList(identifiers) {
  if (!identifiers || identifiers.length === 0) return;
  
  // 并发加载所有问题单的评论预览
  const promises = identifiers.map(async (identifier) => {
    try {
      const res = await fetch(`/api/defects/${identifier}/comments`);
      if (res.ok) {
        const comments = await res.json();
        if (Array.isArray(comments) && comments.length > 0) {
          updateCommentPreview(identifier, comments[0], comments.length);
        } else {
          updateCommentPreview(identifier, null, 0);
        }
      } else {
        updateCommentPreview(identifier, null, 0);
      }
    } catch (e) {
      console.error(`加载问题单 ${identifier} 评论预览失败:`, e);
      updateCommentPreview(identifier, null, 0);
    }
  });
  
  // 等待所有请求完成
  await Promise.all(promises);
}

// 将函数设为全局
window.showDefectCommentModal = showDefectCommentModal;

// 渲染缺陷列表页面
async function renderDefectListPage() {
  const container = document.getElementById('defect-list-content');
  container.innerHTML = '';
  
  // 添加现代化日期选择器样式
  if (!document.getElementById('modern-date-picker-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'modern-date-picker-styles';
    styleSheet.textContent = `
      .modern-date-picker {
        width: 150px;
        padding: 8px 12px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        background-color: #ffffff;
        outline: none;
        transition: all 0.2s ease-in-out;
        cursor: pointer;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      
      .modern-date-picker:hover {
        border-color: #d1d5db;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      
      .modern-date-picker:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
      
      .modern-date-picker.overdue {
        border-color: #ef4444;
        color: #dc2626;
        background-color: #fef2f2;
      }
      
      .modern-date-picker.overdue:hover {
        border-color: #dc2626;
        box-shadow: 0 4px 6px rgba(239, 68, 68, 0.2);
      }
      
      .modern-date-picker.overdue:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
      }
      
      .modern-date-picker::-webkit-calendar-picker-indicator {
        cursor: pointer;
        border-radius: 4px;
        margin-left: 4px;
        opacity: 0.7;
      }
      
      .modern-date-picker::-webkit-calendar-picker-indicator:hover {
        opacity: 1;
        background-color: rgba(59, 130, 246, 0.1);
      }
    `;
    document.head.appendChild(styleSheet);
  }
  
  let html = `<div class="bg-white rounded-xl shadow p-4">
    <div class="text-lg font-bold mb-4">缺陷列表</div>
    
    <!-- 缺陷筛选栏 -->
    <div id="defectFilterBar" class="mb-4 p-4 bg-gray-50 rounded flex flex-wrap gap-4 items-end">
      <div>
        <label class="block text-xs text-gray-500 mb-1">状态</label>
        <select id="defectFilterStatus" class="border rounded px-2 py-1">
          <option value="">全部</option>
          <option value="未关闭" selected>未关闭</option>
          <option value="确认关闭">确认关闭</option>
        </select>
      </div>
      <div>
        <label class="block text-xs text-gray-500 mb-1">负责人</label>
        <select id="defectFilterAssignee" class="border rounded px-2 py-1">
          <option value="">全部</option>
        </select>
      </div>

      <div>
        <label class="block text-xs text-gray-500 mb-1">严重程度</label>
        <select id="defectFilterSeverity" class="border rounded px-2 py-1">
          <option value="">全部</option>
          <option value="致命">致命</option>
          <option value="严重">严重</option>
          <option value="一般">一般</option>
          <option value="提示">提示</option>
        </select>
      </div>

      <div>
        <label class="block text-xs text-gray-500 mb-1">UFS模块</label>
        <select id="defectFilterUFSModule" class="border rounded px-2 py-1">
          <option value="">全部</option>
        </select>
      </div>
      <div>
        <label class="block text-xs text-gray-500 mb-1">提单人</label>
        <select id="defectFilterCreator" class="border rounded px-2 py-1">
          <option value="">全部</option>
        </select>
      </div>
      <div>
        <label class="block text-xs text-gray-500 mb-1">迭代</label>
        <select id="defectFilterIteration" class="border rounded px-2 py-1">
          <option value="">全部</option>
        </select>
      </div>

      <div class="flex gap-2">
        <button id="defectFilterApply" class="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">筛选</button>
        <button id="defectFilterReset" class="px-4 py-1 bg-gray-500 text-white rounded hover:bg-gray-600">重置</button>
      </div>
    </div>
    
    <table class="min-w-full text-sm text-left border" style="table-layout: fixed;">
      <thead><tr class='bg-gray-50'>
        <th class="px-2 py-2 border" style="width: 120px;">编号</th>
        <th class="px-2 py-2 border" style="width: 300px;">标题</th>
        <th class="px-2 py-2 border" style="width: 80px;">状态</th>
        <th class="px-2 py-2 border" style="width: 80px;">负责人</th>
        <th class="px-2 py-2 border" style="width: 60px;">严重程度</th>
        <th class="px-2 py-2 border" style="width: 60px;">UFS模块</th>
        <th class="px-2 py-2 border" style="width: 60px;">提单人</th>
        <th class="px-2 py-2 border" style="width: 80px;">创建时间</th>
        <th class="px-2 py-2 border" style="width: 140px;">计划关闭时间</th>
        <th class="px-2 py-2 border" style="width: 320px;">评论</th>
      </tr></thead>
      <tbody id="defect-list-tbody">
        <tr><td colspan="10" class="text-center text-gray-400">加载中...</td></tr>
      </tbody>
    </table>
    <div id="defect-list-pagination" class="mt-4 flex justify-between items-center">
      <div class="text-sm text-gray-600">
        <span id="defect-list-pagination-info">显示 0-0 条记录，共 0 条</span>
      </div>
      <div class="flex items-center gap-2">
        <button id="defect-list-prev-page" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">上一页</button>
        <div id="defect-list-page-numbers" class="flex gap-1"></div>
        <button id="defect-list-next-page" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed">下一页</button>
      </div>
    </div>
  </div>`;
  container.innerHTML = html;
  
  // 加载数据并使用分页
  await loadAllDefectData();
  
  // 初始化筛选功能
  initDefectFilters();
}

// 初始化缺陷筛选功能
async function initDefectFilters() {
  // 获取所有缺陷数据用于填充筛选选项
  try {
    const res = await fetch('/api/pingcode/defects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page: 1, size: 1000 }) // 获取更多数据用于筛选选项
    });
    if (!res.ok) return;
    const data = await res.json();
    const values = data.values || [];
    
    // 状态选项已在HTML中固定设置，无需动态添加
    
    // 填充负责人选项
    const assigneeSelect = document.getElementById('defectFilterAssignee');
    if (assigneeSelect) {
      // 先清空已有选项（保留"全部"）
      assigneeSelect.innerHTML = '<option value="">全部</option>';
      const assignees = [...new Set(values.map(item => 
        item.assignee ? (item.assignee.display_name || item.assignee.name) : ''
      ).filter(name => name))];
      assignees.sort().forEach(assignee => {
        const option = document.createElement('option');
        option.value = assignee;
        option.textContent = assignee;
        assigneeSelect.appendChild(option);
      });
    }
    
    // 填充提单人选项
    const creatorSelect = document.getElementById('defectFilterCreator');
    if (creatorSelect) {
      // 先清空已有选项（保留"全部"）
      creatorSelect.innerHTML = '<option value="">全部</option>';
      const creators = [...new Set(values.map(item => 
        item.created_by ? (item.created_by.display_name || item.created_by.name) : ''
      ).filter(name => name))];
      creators.sort().forEach(creator => {
        const option = document.createElement('option');
        option.value = creator;
        option.textContent = creator;
        creatorSelect.appendChild(option);
      });
    }
    
    // 填充UFS模块选项
    const ufsModuleSelect = document.getElementById('defectFilterUFSModule');
    if (ufsModuleSelect) {
      // 先清空
      ufsModuleSelect.innerHTML = '<option value="">全部</option>';
      // 动态提取所有实际出现过的模块
      const ufsKeys = [...new Set(values.map(item => {
        const p = item.properties || {};
        return p.UFSmokuai || '';
      }).filter(k => k && UFS_MODULES[k]))];
      ufsKeys.forEach(key => {
        const option = document.createElement('option');
        option.value = UFS_MODULES[key];
        option.textContent = UFS_MODULES[key];
        ufsModuleSelect.appendChild(option);
      });
    }
    
    // 填充前缀选项（迭代、开发等）
    const iterationSelect = document.getElementById('defectFilterIteration');
    if (iterationSelect) {
      iterationSelect.innerHTML = '<option value="">全部</option>';
      const prefixSet = new Set();
      values.forEach(item => {
        const title = item.title || '';
        // 匹配多种前缀格式：【开发】、[迭代0]、[迭代1] 等
        let match = title.match(/^【([^】]+)】/); // 【开发】
        if (!match) {
          match = title.match(/^\[([^\]]+)\]/); // [迭代0]、[迭代1]
        }
        if (match) {
          prefixSet.add(match[1]); // 提取括号内的内容
        }
      });
      
      // 按字母数字顺序排列，迭代类的按数字排序
      Array.from(prefixSet).sort((a, b) => {
        // 如果都是迭代类（迭代0、迭代1），按数字排序
        const aIterMatch = a.match(/^迭代(\d+)$/);
        const bIterMatch = b.match(/^迭代(\d+)$/);
        if (aIterMatch && bIterMatch) {
          return parseInt(aIterMatch[1]) - parseInt(bIterMatch[1]);
        }
        // 否则按字母顺序排序
        return a.localeCompare(b);
      }).forEach(prefix => {
        const option = document.createElement('option');
        option.value = prefix;
        option.textContent = prefix;
        iterationSelect.appendChild(option);
      });
      

    }
    

    
    // 绑定筛选按钮事件
    const applyBtn = document.getElementById('defectFilterApply');
    const resetBtn = document.getElementById('defectFilterReset');
    
    if (applyBtn) {
      applyBtn.onclick = () => applyDefectFilters();
    }
    
    if (resetBtn) {
      resetBtn.onclick = () => resetDefectFilters();
    }
    
    // 初始化完成后，自动应用默认的"未关闭"筛选
    setTimeout(() => {
      applyDefectFilters();
    }, 100);
    
  } catch (error) {
    console.error('初始化筛选功能失败:', error);
  }
}

// 应用缺陷筛选
async function applyDefectFilters() {
  const filterStatus = document.getElementById('defectFilterStatus').value;
  const filterAssignee = document.getElementById('defectFilterAssignee').value;
  const filterSeverity = document.getElementById('defectFilterSeverity').value;
  const filterUFSModule = document.getElementById('defectFilterUFSModule').value;
  const filterCreator = document.getElementById('defectFilterCreator').value;
  const filterIteration = document.getElementById('defectFilterIteration').value;
  
  
  // 获取所有缺陷数据
  try {
    const res = await fetch('/api/pingcode/defects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page: 1, size: 10000 }) // 获取所有数据进行前端筛选
    });
    if (!res.ok) throw new Error('接口请求失败');
    const data = await res.json();
    let values = data.values || [];
    
    // 应用筛选条件
    if (filterStatus) {
      if (filterStatus === '未关闭') {
        values = values.filter(item => {
          const state = item.state ? item.state.name : '';
          return state !== '确认关闭';
        });
      } else {
        values = values.filter(item => item.state && item.state.name === filterStatus);
      }
    }
    
    if (filterAssignee) {
      values = values.filter(item => {
        const assignee = item.assignee ? (item.assignee.display_name || item.assignee.name) : '';
        return assignee === filterAssignee;
      });
    }
    

    
    if (filterSeverity) {
      values = values.filter(item => {
        const p = item.properties || {};
        const severity = DICT.severity[p.severity] || '';
        return severity === filterSeverity;
      });
    }
    
    if (filterUFSModule) {
      values = values.filter(item => {
        const p = item.properties || {};
        let ufsModuleValue = '';
        if (p.UFSmokuai && UFS_MODULES[p.UFSmokuai]) {
          ufsModuleValue = UFS_MODULES[p.UFSmokuai];
        }
        return ufsModuleValue === filterUFSModule;
      });
    }
    
    if (filterCreator) {
      values = values.filter(item => {
        const creator = item.created_by ? (item.created_by.display_name || item.created_by.name) : '';
        return creator === filterCreator;
      });
    }
    
    if (filterIteration) {
      values = values.filter(item => {
        const title = item.title || '';
        // 匹配多种前缀格式：【开发】、[迭代0]、[迭代1] 等
        let match = title.match(/^【([^】]+)】/); // 【开发】
        if (!match) {
          match = title.match(/^\[([^\]]+)\]/); // [迭代0]、[迭代1]
        }
        return match && match[1] === filterIteration;
      });
    }
    

    
    // 渲染筛选后的数据
    renderFilteredDefectData(values);
    
  } catch (error) {
    console.error('筛选缺陷数据失败:', error);
  }
}

// 重置缺陷筛选
function resetDefectFilters() {
  document.getElementById('defectFilterStatus').value = '未关闭'; // 重置后保持默认选择未关闭
  document.getElementById('defectFilterAssignee').value = '';
  document.getElementById('defectFilterSeverity').value = '';
  document.getElementById('defectFilterUFSModule').value = '';
  document.getElementById('defectFilterCreator').value = '';
  document.getElementById('defectFilterIteration').value = '';

  
  // 应用默认筛选（未关闭）
  applyDefectFilters();
}

// 渲染筛选后的缺陷数据
function renderFilteredDefectData(values) {
  const tbody = document.getElementById('defect-list-tbody');
  const paginationInfo = document.getElementById('defect-list-pagination-info');
  const pageNumbers = document.getElementById('defect-list-page-numbers');
  const prevBtn = document.getElementById('defect-list-prev-page');
  const nextBtn = document.getElementById('defect-list-next-page');
  
  if (values.length === 0) {
    tbody.innerHTML = `<tr><td colspan="10" class="text-center text-gray-400">未找到符合条件的数据</td></tr>`;
    paginationInfo.textContent = '显示 0-0 条记录，共 0 条';
    pageNumbers.innerHTML = '';
    prevBtn.disabled = true;
    nextBtn.disabled = true;
    return;
  }
  
  // 简单分页实现（前端分页）
  const pageSize = 20;
  const totalPages = Math.ceil(values.length / pageSize);
  let currentPage = 1;
  
  function renderPage(page) {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pageData = values.slice(start, end);
    
    tbody.innerHTML = pageData.map(item => {
      const identifier = item.identifier || item.whole_identifier || item.id || '';
      const title = item.title || '';
      const state = item.state && item.state.name && item.state.name !== 'N/A' ? item.state.name : '';
      const assignee = item.assignee && (item.assignee.display_name || item.assignee.name) && (item.assignee.display_name || item.assignee.name) !== 'N/A' ? (item.assignee.display_name || item.assignee.name) : '';
      const createdAt = item.created_at ? new Date(item.created_at * 1000).toLocaleDateString('zh-CN') : '';
      const creator = item.created_by && (item.created_by.display_name || item.created_by.name) && (item.created_by.display_name || item.created_by.name) !== 'N/A' ? (item.created_by.display_name || item.created_by.name) : '';
      const p = item.properties || {};
      const severity = p.severity && DICT.severity[p.severity] && DICT.severity[p.severity] !== 'N/A' ? DICT.severity[p.severity] : '';

      let ufsModule = '';
      if (p.UFSmokuai && UFS_MODULES[p.UFSmokuai] && UFS_MODULES[p.UFSmokuai] !== 'N/A') {
        ufsModule = UFS_MODULES[p.UFSmokuai];
      }
      // 计划关闭时间处理
      const storedDates = getPlanClosedDatesFromStorage();
      const storedPlanDate = storedDates[identifier];
      const serverPlanDate = p.jihuaguanbishijian ? new Date(p.jihuaguanbishijian * 1000).toISOString().split('T')[0] : '';
      const planClosedAt = storedPlanDate || serverPlanDate;
      const isOverdue = planClosedAt && new Date(planClosedAt) < new Date() && state !== '确认关闭';
      const isClosed = state === '确认关闭';
      
      return `<tr class="hover:bg-gray-50 transition-colors ${isOverdue ? 'bg-red-50' : ''}">
        <td class='border px-1 py-2 text-blue-600 font-medium' style='width: 120px;'>${identifier}</td>
        <td class='border px-1 py-2 truncate' style='width: 300px;' title="${title}">${title}</td>
        <td class='border px-1 py-2' style='width: 80px;'>
          <span class="px-1 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(state)}">${state}</span>
        </td>
        <td class='border px-1 py-2 truncate' style='width: 80px;' title="${assignee}">${assignee}</td>
        <td class='border px-1 py-2' style='width: 60px;'>
          ${severity ? `<span class=\"px-1 py-1 rounded-full text-xs font-medium ${getSeverityBadgeClass(severity)}\">${severity}</span>` : ''}
        </td>
        <td class='border px-1 py-2 truncate' style='width: 60px;' title="${ufsModule}">${ufsModule}</td>
        <td class='border px-1 py-2 truncate' style='width: 60px;' title="${creator}">${creator}</td>
        <td class='border px-1 py-2 text-gray-600' style='width: 80px;'>${createdAt}</td>
        <td class='border px-1 py-2 ${isOverdue ? 'text-red-600' : 'text-gray-600'}' style='width: 140px;'>
          ${isClosed ? '<span class="text-gray-400">-</span>' : 
            `<input type="date" 
                    value="${planClosedAt}" 
                    class="modern-date-picker ${isOverdue ? 'overdue' : ''}" 
                    style="width: 100%;"
                    onchange="updatePlanClosedDate('${identifier}', this.value)">`
          }
        </td>
        <td class='border px-2 py-2' style='width: 320px; word-wrap: break-word; white-space: normal;'>
          <div class="defect-comment-cell cursor-pointer text-left" 
               onclick="showDefectCommentModal('${identifier}', '${title}')"
               title="点击查看/添加评论">
            <div class="text-xs text-gray-600 leading-relaxed comment-preview-content" 
                 id="comment-preview-${identifier}" 
                 style="word-wrap: break-word; white-space: normal; max-height: 60px; overflow: hidden; position: relative;">
              💬 加载中...
            </div>
          </div>
        </td>
      </tr>`;
    }).join('');
    
    // 渲染完成后，异步加载所有问题单的评论预览
    const identifiers = values.map(item => item.identifier || item.whole_identifier || item.id || '').filter(id => id);
    loadCommentPreviewsForList(identifiers);
    
    // 更新分页信息
    const displayStart = values.length === 0 ? 0 : start + 1;
    const displayEnd = Math.min(end, values.length);
    paginationInfo.textContent = `显示 ${displayStart}-${displayEnd} 条记录，共 ${values.length} 条`;
    
    // 更新分页按钮
    prevBtn.disabled = page <= 1;
    nextBtn.disabled = page >= totalPages;
    prevBtn.onclick = () => { if (page > 1) { currentPage = page - 1; renderPage(currentPage); } };
    nextBtn.onclick = () => { if (page < totalPages) { currentPage = page + 1; renderPage(currentPage); } };
    
    // 更新页码按钮
    let pageNumbersHtml = '';
    const maxVisiblePages = 7;
    let startPage = Math.max(1, page - 3);
    let endPage = Math.min(totalPages, page + 3);
    if (endPage - startPage < maxVisiblePages - 1) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    if (startPage > 1) {
      pageNumbersHtml += `<button class="filtered-page-btn px-3 py-1 rounded bg-gray-200 hover:bg-gray-300" data-page="1">1</button>`;
      if (startPage > 2) pageNumbersHtml += `<span class="px-2 text-gray-500">...</span>`;
    }
    for (let i = startPage; i <= endPage; i++) {
      pageNumbersHtml += `<button class="filtered-page-btn px-3 py-1 rounded ${i === page ? 'bg-blue-500 text-white' : 'bg-gray-200 hover:bg-gray-300'}" data-page="${i}">${i}</button>`;
    }
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) pageNumbersHtml += `<span class="px-2 text-gray-500">...</span>`;
      pageNumbersHtml += `<button class="filtered-page-btn px-3 py-1 rounded bg-gray-200 hover:bg-gray-300" data-page="${totalPages}">${totalPages}</button>`;
    }
    pageNumbers.innerHTML = pageNumbersHtml;
    pageNumbers.querySelectorAll('.filtered-page-btn').forEach(btn => {
      btn.onclick = () => {
        currentPage = parseInt(btn.dataset.page);
        renderPage(currentPage);
      };
    });
  }
  
  // 渲染第一页
  renderPage(1);
}

// 加载所有缺陷数据并使用分页渲染
async function loadAllDefectData() {
  try {
    const res = await fetch('/api/pingcode/defects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page: 1, size: 1000 }) // 获取所有数据
    });
    if (!res.ok) return;
    const data = await res.json();
    const values = data.values || [];
    
    // 使用分页渲染
    renderFilteredDefectData(values);
  } catch (error) {
    console.error('加载缺陷数据失败:', error);
    const tbody = document.getElementById('defect-list-tbody');
    if (tbody) {
      tbody.innerHTML = `<tr><td colspan="11" class="text-center text-red-400">加载失败</td></tr>`;
    }
  }
}

// 拉取缺陷列表数据并渲染表格和分页（备用函数）
async function fetchDefectListData(page = 1) {
  const pageSize = 20;
  const tbody = document.getElementById('defect-list-tbody');
  const paginationInfo = document.getElementById('defect-list-pagination-info');
  const prevBtn = document.getElementById('defect-list-prev-page');
  const nextBtn = document.getElementById('defect-list-next-page');
  const pageNumbers = document.getElementById('defect-list-page-numbers');

  tbody.innerHTML = `<tr><td colspan="10" class="text-center text-gray-400">加载中...</td></tr>`;
  paginationInfo.textContent = '加载中...';

  try {
    const res = await fetch('/api/pingcode/defects', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ page, size: pageSize })
    });
    if (!res.ok) throw new Error('接口请求失败');
    const data = await res.json();
    const values = data.values || [];
    const total = data.total || 0;
    
    if (values.length === 0) {
      tbody.innerHTML = `<tr><td colspan="10" class="text-center text-gray-400">暂无数据</td></tr>`;
    } else {
      tbody.innerHTML = values.map(item => {
        const identifier = item.identifier || item.whole_identifier || item.id || '';
        const title = item.title || '';
        const state = item.state && item.state.name && item.state.name !== 'N/A' ? item.state.name : '';
        const assignee = item.assignee && (item.assignee.display_name || item.assignee.name) && (item.assignee.display_name || item.assignee.name) !== 'N/A' ? (item.assignee.display_name || item.assignee.name) : '';
        const createdAt = item.created_at ? new Date(item.created_at * 1000).toLocaleDateString('zh-CN') : '';
        const creator = item.created_by && (item.created_by.display_name || item.created_by.name) && (item.created_by.display_name || item.created_by.name) !== 'N/A' ? (item.created_by.display_name || item.created_by.name) : '';
        const p = item.properties || {};
        const severity = p.severity && DICT.severity[p.severity] && DICT.severity[p.severity] !== 'N/A' ? DICT.severity[p.severity] : '';

        let ufsModule = '';
        if (p.UFSmokuai && UFS_MODULES[p.UFSmokuai] && UFS_MODULES[p.UFSmokuai] !== 'N/A') {
          ufsModule = UFS_MODULES[p.UFSmokuai];
        }
        // 计划关闭时间处理
        const storedDates = getPlanClosedDatesFromStorage();
        const storedPlanDate = storedDates[identifier];
        const serverPlanDate = p.jihuaguanbishijian ? new Date(p.jihuaguanbishijian * 1000).toISOString().split('T')[0] : '';
        const planClosedAt = storedPlanDate || serverPlanDate;
        const isOverdue = planClosedAt && new Date(planClosedAt) < new Date() && state !== '确认关闭';
        const isClosed = state === '确认关闭';
        
        return `<tr class="hover:bg-gray-50 transition-colors ${isOverdue ? 'bg-red-50' : ''}">
          <td class='border px-1 py-2 text-blue-600 font-medium' style='width: 120px;'>${identifier}</td>
          <td class='border px-1 py-2 truncate' style='width: 300px;' title="${title}">${title}</td>
          <td class='border px-1 py-2' style='width: 80px;'>
            <span class="px-1 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(state)}">${state}</span>
          </td>
          <td class='border px-1 py-2 truncate' style='width: 80px;' title="${assignee}">${assignee}</td>
          <td class='border px-1 py-2' style='width: 60px;'>
            ${severity ? `<span class=\"px-1 py-1 rounded-full text-xs font-medium ${getSeverityBadgeClass(severity)}\">${severity}</span>` : ''}
          </td>
          <td class='border px-1 py-2 truncate' style='width: 60px;' title="${ufsModule}">${ufsModule}</td>
          <td class='border px-1 py-2 truncate' style='width: 60px;' title="${creator}">${creator}</td>
          <td class='border px-1 py-2 text-gray-600' style='width: 80px;'>${createdAt}</td>
          <td class='border px-1 py-2 ${isOverdue ? 'text-red-600' : 'text-gray-600'}' style='width: 140px;'>
            ${isClosed ? '<span class="text-gray-400">-</span>' : 
              `<input type="date" 
                      value="${planClosedAt}" 
                      class="modern-date-picker ${isOverdue ? 'overdue' : ''}" 
                      style="width: 100%;"
                      onchange="updatePlanClosedDate('${identifier}', this.value)">`
            }
          </td>
          <td class='border px-2 py-2' style='width: 320px; word-wrap: break-word; white-space: normal;'>
            <div class="defect-comment-cell cursor-pointer text-left" 
                 onclick="showDefectCommentModal('${identifier}', '${title}')"
                 title="点击查看/添加评论">
              <div class="text-xs text-gray-600 leading-relaxed comment-preview-content" 
                   id="comment-preview-${identifier}" 
                   style="word-wrap: break-word; white-space: normal; max-height: 60px; overflow: hidden; position: relative;">
                💬 加载中...
              </div>
            </div>
          </td>
        </tr>`;
      }).join('');
      
      // 渲染完成后，异步加载所有问题单的评论预览
      const identifiers = values.map(item => item.identifier || item.whole_identifier || item.id || '').filter(id => id);
      loadCommentPreviewsForList(identifiers);
    }
    
    // 渲染分页
    const totalPages = Math.ceil(total / pageSize);
    const start = total === 0 ? 0 : (page - 1) * pageSize + 1;
    const end = Math.min(page * pageSize, total);
    paginationInfo.textContent = `显示 ${start}-${end} 条记录，共 ${total} 条`;
    
    // 上一页/下一页按钮
    prevBtn.disabled = page <= 1;
    nextBtn.disabled = page >= totalPages;
    prevBtn.onclick = () => { if (page > 1) fetchDefectListData(page - 1); };
    nextBtn.onclick = () => { if (page < totalPages) fetchDefectListData(page + 1); };
    
    // 页码按钮
    let pageNumbersHtml = '';
    const maxVisiblePages = 7;
    let startPage = Math.max(1, page - 3);
    let endPage = Math.min(totalPages, page + 3);
    if (endPage - startPage < maxVisiblePages - 1) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    if (startPage > 1) {
      pageNumbersHtml += `<button class="defect-list-page-btn px-3 py-1 rounded bg-gray-200 hover:bg-gray-300" data-page="1">1</button>`;
      if (startPage > 2) pageNumbersHtml += `<span class="px-2 text-gray-500">...</span>`;
    }
    for (let i = startPage; i <= endPage; i++) {
      pageNumbersHtml += `<button class="defect-list-page-btn px-3 py-1 rounded ${i === page ? 'bg-blue-500 text-white' : 'bg-gray-200 hover:bg-gray-300'}" data-page="${i}">${i}</button>`;
    }
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) pageNumbersHtml += `<span class="px-2 text-gray-500">...</span>`;
      pageNumbersHtml += `<button class="defect-list-page-btn px-3 py-1 rounded bg-gray-200 hover:bg-gray-300" data-page="${totalPages}">${totalPages}</button>`;
    }
    pageNumbers.innerHTML = pageNumbersHtml;
    pageNumbers.querySelectorAll('.defect-list-page-btn').forEach(btn => {
      btn.onclick = () => fetchDefectListData(parseInt(btn.dataset.page));
    });
  } catch (e) {
    tbody.innerHTML = `<tr><td colspan="11" class="text-center text-red-500">加载失败: ${e.message}</td></tr>`;
    paginationInfo.textContent = '加载失败';
  }
}

// 获取状态徽章样式
function getStatusBadgeClass(status) {
  switch(status) {
    case '确认关闭': return 'bg-green-100 text-green-800';
    case '待修复': return 'bg-red-100 text-red-800';
    case '回归测试': return 'bg-blue-100 text-blue-800';
    case '根因分析': return 'bg-yellow-100 text-yellow-800';
    case '提交审核': return 'bg-purple-100 text-purple-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}


// 获取严重程度徽章样式
function getSeverityBadgeClass(severity) {
  switch(severity) {
    case '致命': return 'bg-red-100 text-red-800';
    case '严重': return 'bg-orange-100 text-orange-800';
    case '一般': return 'bg-yellow-100 text-yellow-800';
    case '提示': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}
