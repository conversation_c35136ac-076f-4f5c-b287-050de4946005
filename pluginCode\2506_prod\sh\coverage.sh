#!/bin/bash

set -e

# 设置语言环境避免编码问题
export LANG=C
export LC_ALL=C

# 检查参数
if [ $# -eq 0 ]; then
    echo "Usage: $0 <module_name>"
    echo "Available modules: gc, write, mapping, bkm, bm, read, gi, ctl, rb, lib, all"
    exit 1
fi

MODULE=$1
BUILD_DIR="./build-linux"  # 明确构建目录

# 创建临时目录用于存放中间文件
TEMP_DIR="./coverage_temp"
mkdir -p "$TEMP_DIR"

# 清理旧的覆盖率报告
rm -rf coverage_report
rm -rf output/${MODULE}_report
rm -rf "$BUILD_DIR"  # 清理旧构建目录
mkdir -p output/${MODULE}_report

case $MODULE in
    "gc"|"write"|"mapping"|"bkm"|"bm"|"read"|"gi"|"ctl"|"rb"|"lib"|"spor")
        # 运行测试并强制添加覆盖率编译选项
        CXXFLAGS="-fprofile-arcs -ftest-coverage -g" \
        CFLAGS="-fprofile-arcs -ftest-coverage -g" \
        ./run_test.sh $MODULE

        # 生成覆盖率数据（确保gcov文件生成）
        if [ -d "$BUILD_DIR/${MODULE}" ]; then
            cd "$BUILD_DIR/${MODULE}"
            find . -name "*.gcno" -exec gcov -ab {} \;  # 添加-a -b选项生成行和分支覆盖率
            cd ../../
        fi
        ;;
    "all")
        # 运行所有测试并添加覆盖率选项
        CXXFLAGS="-fprofile-arcs -ftest-coverage -g" \
        CFLAGS="-fprofile-arcs -ftest-coverage -g" \
        ./run_test.sh all

        # 生成所有模块覆盖率数据
        for mod in gc write mapping bkm bm read gi ctl rb; do
            if [ -d "$BUILD_DIR/src/ftl/${mod}" ]; then
                cd "$BUILD_DIR/src/ftl/${mod}"
                find . -name "*.gcno" -exec gcov -ab {} \;
                cd ../../../../
            fi
        done
        ;;
    *)
        echo "Unknown module: $MODULE"
        echo "Available modules: gc, write, mapping, bkm, bm, read, gi, ctl, rb, all"
        exit 1
        ;;
esac

# 生成覆盖率报告（使用兼容的错误忽略参数）
echo "收集覆盖率数据..."
lcov --capture --directory "$BUILD_DIR" --output-file "$TEMP_DIR/coverage.info" \
     --ignore-errors gcov \
     --ignore-errors mismatch \
     || true

lcov --remove "$TEMP_DIR/coverage.info" \
    "*/component_googletest/*" \
    "*/component_llt/*" \
    "/usr/*" \
    --output-file "$TEMP_DIR/filtered_coverage.info" \
    --ignore-errors gcov \
    --ignore-errors mismatch \
    --ignore-errors unused \
    || true

genhtml "$TEMP_DIR/filtered_coverage.info" --output-directory "output/${MODULE}_report" \
    --title "FTL Coverage Report - $MODULE" \
    --show-details \
    --legend \
    || true

# 清理临时文件（延迟清理gcov文件以便查看）
rm -rf "$TEMP_DIR"
# find . -name "*.gcov" -type f -delete  # 暂时注释掉，便于调试

# 仅显示报告生成路径，不尝试打开浏览器
if [ -f "output/${MODULE}_report/index.html" ]; then
    echo "覆盖率报告已生成，路径: output/${MODULE}_report/index.html"
else
    echo "覆盖率报告生成失败，请检查上述日志错误"
    echo "建议手动检查gcov文件:"
    find . -name "*.gcov" -type f -print
fi

set +e