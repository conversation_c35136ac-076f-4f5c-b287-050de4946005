<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>任务统计详情</title>
  <!-- 引入依赖 -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <script src="https://unpkg.com/element-plus"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <style>
    .container {
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.05);
    }
    .chart-container {
      width: 100%;
      height: 500px;
      margin: 20px 0;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .detail-dialog .el-table {
      min-height: 400px; //max-height: 400px;
      overflow-y: auto;
    }
    .detail-table .el-table__cell {
      padding: 8px 12px;
    }
    .tooltip-custom {
      padding: 5px 10px;
    }
    .empty-chart {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: #999;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="app" class="container">
    <div class="header">
      <h2>任务状态统计【未关闭: {{processedDataCounts.allNonClosedCounts}} 已超期: {{processedDataCounts.expiredCounts}}】 </h2>
      <el-button
        type="primary"
        size="small"
        @click="loadData"
        :loading="loading"
      >
        {{ loading ? '加载中...' : '刷新数据' }}
      </el-button>
    </div>

    <!-- 柱状图容器 -->
    <div class="chart-container" ref="chartRef">
      <div v-if="isEmptyData" class="empty-chart">暂无数据可展示</div>
    </div>

    <!-- 详情弹窗 --> <!--max-height="80vh"-->
    <el-dialog
      class="detail-dialog"
      :title="currentUser + '的任务详情【' +currentDetail.length+ ' 条数据】' "
      v-model="detailVisible"
      width="80%"
      height="80%"
      @open="handleDialogOpen"
      :destroy-on-close="true"
    >
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="所有未关闭任务" name="allNonClosed"></el-tab-pane>
        <el-tab-pane label="已过期任务" name="expired"></el-tab-pane>

        <el-table
          v-if="detailVisible && currentDetail.length"
          :data="currentDetail"
          border
          class="detail-table"
        >
          <el-table-column prop="identifier" label="编号" width="100"></el-table-column>
          <el-table-column prop="title" label="标题" min-width="200"></el-table-column>
          <el-table-column prop="state" label="状态" width="110">
            <template #default="scope" >
              <el-tag :type="stateTypeMap[scope.row.state] || 'info'">
                {{ scope.row.state }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_by" label="创建人" width="120"></el-table-column>
          <el-table-column prop="assignee" label="负责人" width="120"></el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="scope">
              <span>
                {{ scope.row.created_at }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="end_at" label="截止时间" width="160">
            <template #default="scope">
              <span :style="{ color: scope.row.end_at !== '无' && isExpired(scope.row.end_at) ? 'red' : '' }">
                {{ scope.row.end_at }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="project" label="项目" width="150"></el-table-column>
        </el-table>

        <div v-if="detailVisible && !currentDetail.length" class="empty-state">
          <el-empty description="暂无相关任务数据"></el-empty>
        </div>
      </el-tabs>
    </el-dialog>
  </div>

  <script>
    const { createApp, markRaw } = Vue;
    createApp({
      data() {
        return {
          loading: false,
          chart: null,
          chartData: {},
          isEmptyData: false,
          processedData: {
            sortedUsers: [],
            allNonClosedCounts: [],
            expiredCounts: []
          },
          detailVisible: false,
          currentUser: '',
          currentDetail: [],
          activeTab: 'allNonClosed',
          stateTypeMap: {
            '打开': 'info',
            '进行中': 'primary',
            '已解决': 'success',
            '关闭': 'default',
            '已取消': 'warning'
          },
          processedDataCounts:{
            sortedUsers:0, 
            allNonClosedCounts:0, 
            expiredCounts:0
          },
        }
      },
      mounted() {
        this.$nextTick(() => {
          this.initChart();
          window.addEventListener('resize', this.handleResize);
          this.loadData();
        });
      },
      beforeUnmount() {
        if (this.chart) {
          this.chart.dispose();
        }
        window.removeEventListener('resize', this.handleResize);
      },
      methods: {
        initChart() {
          if (this.chart) {
            this.chart.dispose();
          }
          const dom = this.$refs.chartRef;
          this.chart = markRaw(echarts.init(dom));
        },
        handleResize() {
          if (this.chart) {
            this.chart.resize();
          }
        },
        async loadData() {
          this.loading = true;
          try {
            const response = await fetch('http://10.20.93.252:9002/api/filtered-tasks');
            if (!response.ok) throw new Error(`请求失败: ${response.status}`);

            const result = await response.json();
            this.chartData = result.groupedByUser || {};

            if (Object.keys(this.chartData).length === 0) {
              this.isEmptyData = true;
              return;
            }

            this.processChartData();
            this.renderChart();
          } catch (error) {
            console.error('加载失败', error);
            this.isEmptyData = true;
          } finally {
            this.loading = false;
          }
        },
        processChartData() {
          const userList = Object.keys(this.chartData);
          if (userList.length === 0) {
            this.processedData = {
              sortedUsers: [],
              allNonClosedCounts: [],
              expiredCounts: []
            };
            this.isEmptyData = true;
            return;
          }

          const sortedUsers = userList.sort((a, b) => {
            return (this.chartData[b]?.allNonClosed?.length || 0) -
                   (this.chartData[a]?.allNonClosed?.length || 0);
          });

          this.processedData = {
            sortedUsers,
            allNonClosedCounts: sortedUsers.map(user =>
              this.chartData[user]?.allNonClosed?.length || 0),
            expiredCounts: sortedUsers.map(user =>
              this.chartData[user]?.expired?.length || 0)
          };
        },
        renderChart() {
          if (!this.chart) return;

          const { sortedUsers, allNonClosedCounts, expiredCounts } = this.processedData;
          if (sortedUsers.length === 0) {
            this.isEmptyData = true;
            return;
          }

           this.processedDataCounts.sortedUsers = sortedUsers.length;
           this.processedDataCounts.allNonClosedCounts = allNonClosedCounts.reduce((a,b)=>a+b,0);
           this.processedDataCounts.expiredCounts = expiredCounts.reduce((a,b)=>a+b,0);
           
          console.log(this.processedDataCounts);

          this.isEmptyData = false;
          this.chart.showLoading();

          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: { type: 'shadow' },
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderColor: '#ddd',
              borderWidth: 1,
              textStyle: { color: '#333' },
              formatter: (params) => {
                const idx = params[0].dataIndex;
                return `
                  <div class="tooltip-custom">
                    <strong>${sortedUsers[idx]}</strong><br>
                    <span style="color: #409EFF">● 未关闭任务：${allNonClosedCounts[idx]}个</span><br>
                    <span style="color: #F56C6C">● 已过期任务：${expiredCounts[idx]}个</span>
                  </div>
                `;
              }
            },
            legend: {
              data: ['未关闭任务', '已过期任务'],
              top: 0
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: sortedUsers,
              name: '用户',
              axisLabel: {
                rotate: 45,
                interval: 0
              }
            },
            yAxis: {
              type: 'value',
              name: '任务数量',
              minInterval: 1
            },
            series: [
              {
                name: '未关闭任务',
                type: 'bar',
                data: allNonClosedCounts,
                itemStyle: { color: '#409EFF' },
                barWidth: '30%',
                label: {
                  show: true,
                  position: 'top',
                  color: '#333',
                  fontSize: 12
                }
              },
              {
                name: '已过期任务',
                type: 'bar',
                data: expiredCounts,
                itemStyle: { color: '#F56C6C' },
                barWidth: '30%',
                label: {
                  show: true,
                  position: 'top',
                  color: '#333',
                  fontSize: 12
                }
              }
            ]
          };

          // 使用markRaw避免响应式代理干扰
          this.chart.setOption(markRaw(option));
          this.chart.hideLoading();

          // 确保在渲染完成后绑定事件
          this.chart.off('finished');
          this.chart.off('click');
          this.chart.on('finished', this.bindChartEvents);
        },
        bindChartEvents() {
          this.chart.off('click');
          this.chart.on('click', (params) => {
            const idx = params.dataIndex;
            this.currentUser = this.processedData.sortedUsers[idx];
            this.activeTab = params.seriesName === '未关闭任务' ? 'allNonClosed' : 'expired';
            this.currentDetail = this.chartData[this.currentUser]?.[this.activeTab] || [];
            this.detailVisible = true;
          });
        },
        handleDialogOpen() {
          this.currentDetail = this.chartData[this.currentUser]?.[this.activeTab] || [];
        },
        handleTabChange(tabName) {
          this.currentDetail = this.chartData[this.currentUser]?.[tabName] || [];
        },
        isExpired(endTime) {
          if (endTime === '无' || !endTime) return false;
          const taskDate = new Date(endTime);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return taskDate < today;
        }
      }
    }).use(ElementPlus).mount('#app');
  </script>
</body>
</html>
