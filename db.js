const mysql = require('mysql2');

const pool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'zhangkun',
  charset: 'utf8mb4',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 在连接池创建后，强制执行 SET NAMES utf8mb4
pool.on('connection', (connection) => {
  connection.query("SET NAMES utf8mb4", (err) => {
    if (err) console.error("设置数据库连接编码失败:", err);
    else console.log("连接设置为 utf8mb4 成功");
  });
});

// 创建建议表的SQL语句
const createSuggestionsTableSQL = `
CREATE TABLE IF NOT EXISTS suggestions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  suggestion_text TEXT NOT NULL,
  submitted_by VARCHAR(100) NOT NULL,
  submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('待处理', '已采纳', '处理中', '已完成') DEFAULT '待处理',
  done_at DATETIME DEFAULT NULL,
  deadline DATETIME DEFAULT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;

// 创建评论表的SQL语句
const createSuggestionCommentsTableSQL = `
CREATE TABLE IF NOT EXISTS suggestion_comments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  suggestion_id INT NOT NULL,
  comment_text TEXT NOT NULL,
  comment_by VARCHAR(100) NOT NULL,
  comment_by_name VARCHAR(100) NOT NULL,
  comment_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (suggestion_id) REFERENCES suggestions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;

// 创建问题单评论表的SQL语句
const createDefectCommentsTableSQL = `
CREATE TABLE IF NOT EXISTS defect_comments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  defect_identifier VARCHAR(50) NOT NULL,
  comment_text TEXT NOT NULL,
  comment_by VARCHAR(100) NOT NULL,
  comment_by_name VARCHAR(100) NOT NULL,
  comment_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_defect_identifier (defect_identifier)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;

// 新增：GitLab构建统计表
const createGitlabBuildsTableSQL = `
CREATE TABLE IF NOT EXISTS gitlab_builds (
  id INT PRIMARY KEY,
  status VARCHAR(32),
  created_at DATETIME,
  duration FLOAT,
  title VARCHAR(512)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;

// 如表已存在但缺title字段，补充ALTER TABLE
pool.getConnection((err, connection) => {
  if (!err) {
    // 先查字段是否存在
    connection.query("SHOW COLUMNS FROM gitlab_builds LIKE 'title'", (e, results) => {
      if (e) { console.error('检查title字段失败:', e); connection.release(); return; }
      if (results.length === 0) {
        // 不存在才添加
        connection.query("ALTER TABLE gitlab_builds ADD COLUMN title VARCHAR(512)", (e2) => {
          if (e2 && !String(e2).includes('Duplicate')) console.error('添加title字段失败:', e2);
          else console.log('已成功添加title字段');
          connection.release();
        });
      } else {
        connection.release();
      }
    });
  }
});

// 初始化数据库表
function initializeDatabase() {
  pool.getConnection((err, connection) => {
    if (err) {
      console.error('获取数据库连接失败:', err);
      return;
    }

    // 创建建议表
    connection.query(createSuggestionsTableSQL, (err, results) => {
      if (err) {
        console.error('创建建议表失败:', err);
      } else {
        console.log('建议表初始化成功');
      }
    });
    // 创建评论表
    connection.query(createSuggestionCommentsTableSQL, (err, results) => {
      if (err) {
        console.error('创建评论表失败:', err);
      } else {
        console.log('评论表初始化成功');
      }
    });
    // 创建问题单评论表
    connection.query(createDefectCommentsTableSQL, (err, results) => {
      if (err) {
        console.error('创建问题单评论表失败:', err);
      } else {
        console.log('问题单评论表初始化成功');
      }
    });
    // 新增：创建GitLab构建统计表
    connection.query(createGitlabBuildsTableSQL, (err, results) => {
      if (err) {
        console.error('创建GitLab构建统计表失败:', err);
      } else {
        console.log('GitLab构建统计表初始化成功');
      }
      connection.release();
    });
  });
}

// 每小时ping一次数据库，保持连接活跃
setInterval(() => {
  pool.getConnection((err, connection) => {
    if (err) {
      console.error('ping数据库连接池错误:', err);
      return;
    }
    
    // 执行一个简单的查询来保持连接活跃
    connection.ping((pingErr) => {
      connection.release(); // 释放连接
      
      if (pingErr) {
        console.error('ping数据库错误:', pingErr);
      } else {
        console.log('数据库ping成功，连接保持活跃');
      }
    });
  });
}, 3600000); // 每小时执行一次

// 初始化数据库
initializeDatabase();

module.exports = pool;
