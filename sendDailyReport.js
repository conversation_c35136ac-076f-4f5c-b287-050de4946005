const nodemailer = require('nodemailer');
const mysql = require('mysql2/promise');
const fs = require('fs');
const cheerio = require('cheerio');
const path = require('path');
const axios = require('axios');

// 邮箱配置
const transporter = nodemailer.createTransport({
  host: 'smtp.exmail.qq.com',
  port: 465,
  secure: true,
  auth: {
    user: '<EMAIL>',
    pass: 'Dml.adit123'
  }
});

// 数据库配置
const dbPool = mysql.createPool({
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'zhangkun',
  charset: 'utf8mb4'
});

// 模块列表
const modules = [
  { key: 'gc', name: 'GC' },
  { key: 'write', name: 'Write' },
  { key: 'mapping', name: 'Mapping' },
  { key: 'bkm', name: 'BK<PERSON>' },
  { key: 'bm', name: '<PERSON><PERSON>' },
  { key: 'read', name: 'Read' },
  { key: 'gi', name: 'G<PERSON>' },
  { key: 'ctl', name: 'CT<PERSON>' },
  { key: 'rb', name: 'RB' },
  { key: 'spor', name: 'SPOR' }
];

// 负责人映射
const moduleManager = {
  gc: "刘军、邵宇杰",
  write: "龙家、马俊凯",
  mapping: "李腾、游世龙",
  bkm: "陈伟、洪灵杰",
  bm: "洪灵杰",
  read: "柴敏杰、倪李彬",
  gi: "洪灵杰",
  ctl: "潘晓彬",
  rb: "芈国政",
  spor: "洪灵杰"
};

// 删除 extractModuleData 函数（已不用）

// 新增：直接从GitLab API获取动态的红色柱子数据
async function fetchUnclosedOpinionsFromGitlab(ownershipRules) {
  try {
    console.log('🌐 开始从GitLab API获取动态的红色柱子数据...');
    
    const gitlabUrl = 'http://***********/api/v4';
    const projectId = 'qh/qh';
    const privateToken = '**************************';
    const state = 'all'; // 获取所有状态的MR，与server.js保持一致
    
    let allMergeRequests = [];
    let page = 1;
    const perPage = 100;
    let hasMore = true;
    
    // 1. 拉取所有未关闭的MR
    while (hasMore) {
      const encodedProjectId = encodeURIComponent(projectId);
      const url = `${gitlabUrl}/projects/${encodedProjectId}/merge_requests?scope=all&state=${state}&page=${page}&per_page=${perPage}`;
      
      const response = await axios.get(url, {
        headers: {
          'PRIVATE-TOKEN': privateToken
        },
        timeout: 30000
      });
      
      const mergeRequests = response.data;
      allMergeRequests = allMergeRequests.concat(mergeRequests);
      hasMore = mergeRequests.length === perPage;
      page++;
    }
    
    console.log(`📋 获取到 ${allMergeRequests.length} 个未关闭的MR`);
    
    // 2. 对每个MR拉取discussions，获取意见
    const opinionsByUser = {}; // 按用户分组存储意见
    
    for (const mr of allMergeRequests) {
      try {
        // 获取MR详情
        const mrDetailUrl = `${gitlabUrl}/projects/${encodeURIComponent(projectId)}/merge_requests/${mr.iid}`;
        const mrDetailResponse = await axios.get(mrDetailUrl, {
          headers: { 'PRIVATE-TOKEN': privateToken }
        });
        const mrDetail = mrDetailResponse.data;
        
        const mrAuthorUsername = mrDetail.author.username;
        const mrTitle = mrDetail.title || '';
        const mrAuthorName = mrDetail.author.name || '';
        const mrCreatedAt = mrDetail.created_at || '';
        
        // 获取MR的discussions
        let discussionsPage = 1;
        let discussionsHasMore = true;
        
        while (discussionsHasMore) {
          const discussionsUrl = `${gitlabUrl}/projects/${encodeURIComponent(projectId)}/merge_requests/${mr.iid}/discussions?per_page=${perPage}&page=${discussionsPage}`;
          const discussionsResponse = await axios.get(discussionsUrl, {
            headers: { 'PRIVATE-TOKEN': privateToken }
          });
          
          const discussions = discussionsResponse.data;
          
          for (const discussion of discussions) {
            // 找到所有顶级意见（system: false, 无 in_reply_to_id），取 created_at 最早的（楼主）
            const topLevelNotes = (discussion.notes || []).filter(note => 
              note.system === false && (!note.in_reply_to_id || note.in_reply_to_id === null)
            );
            
            if (topLevelNotes.length === 0) continue;
            
            const firstTopLevel = topLevelNotes.reduce((a, b) => 
              new Date(a.created_at) < new Date(b.created_at) ? a : b
            );
            
            const name = firstTopLevel.author.name || firstTopLevel.author.username || '未知';
            
            // 检查意见是否已关闭
            // 通过检查topLevelNote的resolved状态来判断（与server.js保持一致）
            const isResolved = firstTopLevel.resolved || false;
            const resolvedAt = firstTopLevel.resolved_at;
            const resolvedBy = firstTopLevel.resolved_by;
            
            // 只统计未关闭的意见
            if (isResolved) {
              continue; // 静默跳过已关闭的意见
            }
            
            // 提取文件路径
            let filePath = '';
            if (firstTopLevel.position && firstTopLevel.position.new_path) {
              filePath = firstTopLevel.position.new_path;
            }
            
            // 只处理.c和.h文件
            if (!filePath || (!filePath.endsWith('.c') && !filePath.endsWith('.h'))) {
              continue; // 跳过非.c和.h文件
            }
            
            // 根据文件路径查找归属用户
            const fileOwner = findFileOwner(filePath, ownershipRules);
            
            // 如果找到文件归属用户，这个意见就属于该用户
            if (fileOwner) {
              // 只为责任田内的未关闭意见添加调试信息
              console.log(`   🔍 发现未关闭意见: ${name} - ${firstTopLevel.body?.substring(0, 30)}...`);
              console.log(`      - 文件路径: ${filePath || '未知'}`);
              console.log(`      - 意见内容: ${firstTopLevel.body?.substring(0, 50)}...`);
              console.log(`      - 文件归属: ${fileOwner}`);
              
              const opinion = {
                mr_iid: mr.iid,
                reviewer: name,
                body: firstTopLevel.body,
                created_at: firstTopLevel.created_at,
                isMrAuthor: false,
                mr_title: mrTitle,
                mr_author: mrAuthorName,
                mr_created_at: mrCreatedAt,
                file_path: filePath,
                file_owner: fileOwner,
                isResolved: false,
                resolvedAt: null,
                resolvedBy: null
              };
              
              // 按文件归属用户分组存储意见
              if (!opinionsByUser[fileOwner]) {
                opinionsByUser[fileOwner] = [];
              }
              opinionsByUser[fileOwner].push(opinion);
            }
            // 静默跳过不在责任田范围内的文件
          }
          
          discussionsHasMore = discussions.length === perPage;
          discussionsPage++;
        }
      } catch (error) {
        console.error(`❌ 获取MR ${mr.iid} 的意见失败:`, error.message);
      }
    }
    
    console.log(`✅ 从GitLab API获取到 ${Object.values(opinionsByUser).flat().length} 个动态未关闭意见（红色柱子）`);
    
    // 按reviewer统计
    const reviewerCounts = {};
    Object.keys(opinionsByUser).forEach(name => {
      reviewerCounts[name] = (reviewerCounts[name] || 0) + opinionsByUser[name].length;
    });
    
    console.log('📊 红色柱子数据分布:');
    Object.entries(reviewerCounts).forEach(([name, count]) => {
      console.log(`   - ${name}: ${count}个未关闭意见`);
    });
    
    return opinionsByUser;
    
  } catch (error) {
    console.error('❌ 从GitLab API获取数据失败:', error.message);
    console.log('⚠️ 无法获取红色柱子数据，返回空数组');
    return {};
  }
}

// 新增：从farmland.yml加载文件归属规则
function loadFileOwnershipRules() {
  try {
    const farmlandPath = path.join(__dirname, 'farmland.yml');
    const farmlandContent = fs.readFileSync(farmlandPath, 'utf-8');
    const rules = {};
    
    // 解析YAML内容
    const lines = farmlandContent.split('\n');
    let currentOwner = '';
    let currentModuleName = '';
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('Owner:')) {
        currentOwner = trimmedLine.replace('Owner:', '').trim().replace(/"/g, '');
      } else if (trimmedLine.startsWith('ModuleName:')) {
        currentModuleName = trimmedLine.replace('ModuleName:', '').trim().replace(/"/g, '');
      } else if (trimmedLine.startsWith('- "') && currentOwner) {
        const filePath = trimmedLine.replace('- "', '').replace('"', '');
        rules[filePath] = currentOwner;
      }
    }
    
    console.log('📋 加载文件归属规则:', Object.keys(rules).length, '条');
    return rules;
  } catch (error) {
    console.error('❌ 加载farmland.yml失败:', error.message);
    return {};
  }
}

// 新增：根据文件路径查找归属用户
function findFileOwner(filePath, ownershipRules) {
  if (!filePath) return null;
  
  // 统一路径分隔符，去除开头的'./'
  const gitlabPath = filePath.replace(/\\/g, '/').replace(/^\.\//, '');
  
  // 核心修正：如果路径以'src/'开头，则在比较时忽略这个前缀
  let pathToCompare = gitlabPath;
  if (gitlabPath.startsWith('src/')) {
    pathToCompare = gitlabPath.substring(4);
  }
  
  // 遍历查找传入的文件属于哪个模块
  for (const [rulePath, owner] of Object.entries(ownershipRules)) {
    // 统一yml中的路径分隔符
    const normalizedModulePath = rulePath.replace(/\\/g, '/').replace(/^\/+/, '').replace(/^\.\//, '');
    
    // 使用修正后的路径进行健壮的匹配
    // 1. 匹配目录 (e.g., 'ftl/bkm/bkm_rtbb.c' starts with 'ftl/bkm/')
    if (pathToCompare.startsWith(normalizedModulePath + '/')) {
      return owner;
    }
    // 2. 匹配文件 (e.g., 'ftl/bkm.c' == 'ftl/bkm.c')
    if (pathToCompare === normalizedModulePath) {
      return owner;
    }
  }
  
  return null;
}

async function main() {
  // 查询所有用户，获取 email 和 name
  const [users] = await dbPool.query('SELECT email, name FROM users WHERE email IS NOT NULL');

  // 加载文件归属规则
  const ownershipRules = loadFileOwnershipRules();

  // 从GitLab API获取动态的红色柱子数据
  const unclosedOpinions = await fetchUnclosedOpinionsFromGitlab(ownershipRules);

  // 添加调试信息
  console.log('📊 意见数据统计:');
  const totalOpinionsCount = Object.values(unclosedOpinions).flat().length;
  console.log(`   - 总意见数量: ${totalOpinionsCount}`);
  
  // 按文件归属人统计（红色柱子数据）
  const fileOwnerCount = {};
  Object.values(unclosedOpinions).flat().forEach(op => {
    const fileOwner = op.file_owner || '未知';
    fileOwnerCount[fileOwner] = (fileOwnerCount[fileOwner] || 0) + 1;
  });
  
  console.log('👥 按文件归属人统计未关闭意见（红色柱子）:');
  Object.entries(fileOwnerCount).forEach(([name, count]) => {
    console.log(`   - ${name}: ${count}个`);
  });
  
  console.log('📝 意见数据示例:');
  Object.values(unclosedOpinions).flat().slice(0, 3).forEach((op, idx) => {
    console.log(`   ${idx + 1}. 文件归属: ${op.file_owner} - 评论人: ${op.reviewer} - MR#${op.mr_iid} - ${op.body?.substring(0, 50)}...`);
  });

  // 1. GoogleTest 覆盖率表格（新版：解析all_index_run2.html）
  const allIndexPath = path.join(__dirname, 'pluginCode/2506_prod/output_html/index/all_index_run2.html');
  let coverageRows = [];
  let thresholdMap = {};
  if (fs.existsSync(allIndexPath)) {
    const html = fs.readFileSync(allIndexPath, 'utf-8');
    const $ = cheerio.load(html);
    // 解析阈值map（mode_threshold_value）
    const scriptText = $('script').html() || '';
    const thresholdMatch = scriptText.match(/mode_threshold_value\s*:\s*({[\s\S]*?})/);
    if (thresholdMatch) {
      try {
        // 用eval解析对象字面量
        thresholdMap = eval('(' + thresholdMatch[1] + ')');
      } catch (e) {
        thresholdMap = {};
      }
    }
    // 解析表格数据
    // 由于前端用Vue渲染，直接抓静态HTML表格不行，需要用js逻辑模拟
    // 这里直接复用前端的模块列表和负责人
    for (const mod of modules) {
      // 兼容模块名带" - "分隔的情况
      const filePath = path.join(__dirname, `pluginCode/2506_prod/output_html/output/${mod.key}_report/index.html`);
      if (!fs.existsSync(filePath)) continue;
      const modHtml = fs.readFileSync(filePath, 'utf-8');
      const _$ = cheerio.load(modHtml);
      const mainTable = _$("table").first().find("table").first();
      const rows = mainTable.find("tr");
      if (rows.length < 3) continue;
      const row2 = rows.eq(1).find('td'); // 代码覆盖率
      const row3 = rows.eq(2).find('td'); // 函数覆盖率
      let moduleName = row2.eq(1).text().replace('FTL Coverage Report - ', '').trim() || mod.key;
      let moduleSort = moduleName.split(' - ')[1] ? moduleName.split(' - ')[1].toString() : moduleName;
      const threshold = thresholdMap[moduleSort] || 80;
      const lineVo = row2.eq(4).text().trim();
      const lineVoNum = parseFloat(lineVo.replace('%',''));
      const lineVoRate = `${row2.eq(6).text().trim()}/${row2.eq(5).text().trim()}`;
      const funVo = row3.eq(4).text().trim();
      const funVoRate = `${row3.eq(6).text().trim()}/${row3.eq(5).text().trim()}`;
      const time = row3.eq(1).text().trim();
      coverageRows.push({
        module: moduleName,
        people: moduleManager[moduleSort] || '',
        lineVo,
        lineVoNum,
        threshold,
        lineVoRate,
        funVo,
        funVoRate,
        time
      });
    }
  }

  // 生成GoogleTest覆盖率表格HTML
  const googleTestTableHtml = `
    <h2>googleTest覆盖率</h2>
    <table border="1" cellpadding="4" cellspacing="0" style="border-collapse:collapse;">
      <tr>
        <th>序号</th>
        <th>模块</th>
        <th>模块负责人</th>
        <th>代码行覆盖率[Lines]</th>
        <th>阈值[%]</th>
        <th>Hit/Total</th>
        <th>函数覆盖率[Functions]</th>
        <th>Hit/Total</th>
        <th>报告生成时间</th>
      </tr>
      ${coverageRows.map((row, idx) => `
        <tr>
          <td>${idx + 1}</td>
          <td>${row.module}</td>
          <td>${row.people}</td>
          <td${row.lineVoNum < row.threshold ? ' style="color:#f56c6c;font-weight:bold;"' : ''}>${row.lineVo}</td>
          <td>${row.threshold.toFixed(1)}%</td>
          <td>${row.lineVoRate}</td>
          <td>${row.funVo}</td>
          <td>${row.funVoRate}</td>
          <td>${row.time}</td>
        </tr>
      `).join('')}
    </table>
  `;

  // 获取未关闭的意见
  console.log('获取到的未关闭意见数量:', totalOpinionsCount);
  console.log('未关闭意见数据示例:', Object.values(unclosedOpinions).flat().slice(0, 2));

  // 4. 直接使用已分组的意见数据（用于邮件发送）
  // unclosedOpinions 已经是按 file_owner 分组的数据
  const unclosedByPerson = unclosedOpinions;

  console.log('按文件归属人分组的意见数量:', Object.keys(unclosedByPerson).length);
  console.log('文件归属人列表:', Object.keys(unclosedByPerson));

  // 显示数据库用户列表
  console.log('👤 数据库用户列表:');
  users.forEach(user => {
    console.log(`   - ${user.name} (${user.email})`);
  });

  // 显示意见数据中的文件归属人列表
  console.log('📋 意见数据中的文件归属人列表:');
  const fileOwnerList = [...new Set(Object.values(unclosedOpinions).flat().map(op => op.file_owner))];
  fileOwnerList.forEach(fileOwner => {
    console.log(`   - ${fileOwner}`);
  });

  // 5. 群发邮件
  console.log('\n📧 开始发送邮件...');
  console.log(`📋 数据库用户数量: ${users.length}`);
  
  // 添加详细的匹配调试信息
  console.log('\n🔍 用户匹配调试信息:');
  console.log('数据库用户列表:');
  users.forEach(user => {
    console.log(`   - "${user.name}" (${user.email})`);
  });
  
  console.log('\nGitLab文件归属人列表:');
  Object.keys(unclosedByPerson).forEach(fileOwner => {
    console.log(`   - "${fileOwner}"`);
  });
  
  console.log('\n匹配结果:');
  for (const user of users) {
    const hasOpinions = unclosedByPerson[user.name];
    const similarUsers = Object.keys(unclosedByPerson).filter(name => 
      name.includes(user.name) || user.name.includes(name)
    );
    console.log(`   - "${user.name}": ${hasOpinions ? `✅ 匹配到 ${hasOpinions.length} 个意见` : `❌ 未匹配`}${similarUsers.length > 0 ? ` (相似用户名: ${similarUsers.join(', ')})` : ''}`);
  }
  
  for (const user of users) {
    console.log(`\n👤 处理用户: ${user.name} (${user.email})`);
    
    let mailContent = `
      <div style="font-family: '微软雅黑', 'Arial', sans-serif;">
        ${googleTestTableHtml}
    `;
    
    // 如果该用户有未关闭意见，插入表格
    if (unclosedByPerson[user.name]) {
      const opinions = unclosedByPerson[user.name];
      console.log(`   ✅ 用户 ${user.name} 有 ${opinions.length} 个未关闭意见`);
      
      mailContent += `
        <br><h2>您的未关闭意见</h2>
        <table border="1" cellpadding="4" cellspacing="0" style="border-collapse:collapse;">
          <tr>
            <th>创建时间</th>
            <th>MR编号</th>
            <th>MR标题</th>
            <th>MR作者</th>
            <th>MR创建时间</th>
            <th>内容</th>
            <th>文件路径</th>
          </tr>
          ${opinions.map(op => `
            <tr>
              <td>${op.created_at || ''}</td>
              <td>${op.mr_iid || ''}</td>
              <td>${op.mr_title || ''}</td>
              <td>${op.mr_author || ''}</td>
              <td>${op.mr_created_at || ''}</td>
              <td>${op.body || ''}</td>
              <td>${op.file_path || ''}</td>
            </tr>
          `).join('')}
        </table>
      `;
    } else {
      console.log(`   ⚠️ 用户 ${user.name} 没有未关闭意见`);
      // 检查是否有相似的用户名
      const similarUsers = Object.keys(unclosedByPerson).filter(name => 
        name.includes(user.name) || user.name.includes(name)
      );
      if (similarUsers.length > 0) {
        console.log(`   💡 发现相似用户名: ${similarUsers.join(', ')}`);
      }
    }
    
    mailContent += '</div>';

    await transporter.sendMail({
      from: '<EMAIL>',
      to: user.email,
      subject: '每日googleTest覆盖率',
      html: mailContent,
      headers: {
        'Content-Type': 'text/html; charset=UTF-8'
      }
    });
    console.log(`   📤 已发送给 ${user.email}`);
  }
}

// 定时发送功能
function scheduleDailyReport() {
  console.log('📅 启动定时发送功能...');
  console.log('⏰ 每天早上8:30自动发送邮件给所有用户');
  
  // 计算到明天早上8:30的时间
  function getNextRunTime() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(8, 30, 0, 0); // 8:30 AM
    
    // 如果今天还没到8:30，就今天发送
    const today = new Date(now);
    today.setHours(8, 30, 0, 0);
    
    return now < today ? today : tomorrow;
  }
  
  function runDailyReport() {
    console.log('⏰ 定时任务触发，开始发送每日报告...');
    console.log('📅 当前时间:', new Date().toLocaleString());
    
    main().catch(error => {
      console.error('❌ 定时发送失败:', error);
    });
    
    // 设置下次执行时间
    const nextRun = getNextRunTime();
    console.log('📅 下次执行时间:', nextRun.toLocaleString());
    
    const timeUntilNext = nextRun.getTime() - Date.now();
    setTimeout(runDailyReport, timeUntilNext);
  }
  
  // 设置定时执行
  const nextRun = getNextRunTime();
  console.log('📅 下次定时执行时间:', nextRun.toLocaleString());
  
  const timeUntilNext = nextRun.getTime() - Date.now();
  setTimeout(runDailyReport, timeUntilNext);
}

// 启动定时功能
scheduleDailyReport();
