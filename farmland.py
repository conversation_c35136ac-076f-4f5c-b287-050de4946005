#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import yaml

class Farmland:
    """
    Farmland 模块类，用于表示一个 Farmland 模块。
    :param root_path: 需要搜索文件的根路径，和Farmland里面相对路径进行拼接使用
    :param farmland_file_path: Farmland 文件的路径，必须是yaml格式
    """
    def __init__(self, root_path, farmland_file_path):
        self.root_path = os.path.abspath(root_path)
        self.file_path = os.path.abspath(farmland_file_path)
        # 读取 YAML 文件
        with open(self.file_path, 'r', encoding='utf-8') as f:
            self.farmland_data = yaml.safe_load(f)
        # self.print_farmland()

    def print_farmland(self):
        # 遍历并打印整个 Farmland 模块的信息
        for index, module in enumerate(self.farmland_data["Farmland"], 1):
            print(f"  ModuleName: {module['ModuleName']}")
            print(f"  Owner: {module['Owner']}")
            print(f"  FilePath:")

    def get_info_by_file_path(self, check_file_path):
        """
        根据文件路径获取 Farmland 模块信息
        :param check_file_path: 需要检查的文件路径，可以是相对路径或绝对路径
        :return: Farmland 模块信息
        """
        # 统一 GitLab 传来的路径分隔符
        gitlab_path = check_file_path.replace('\\\\', '/').lstrip('./')

        # 核心修正：如果路径以'src/'开头，则在比较时忽略这个前缀
        path_to_compare = gitlab_path
        if gitlab_path.startswith('src/'):
            path_to_compare = gitlab_path[4:]

        # 遍历查找传入的文件属于哪个模块
        for modules in self.farmland_data["Farmland"]:
            if 'FilePath' not in modules:
                continue

            for module_file_path in modules['FilePath']:
                # 统一 yml 中的路径分隔符
                normalized_module_path = module_file_path.replace('\\\\', '/').strip('/').lstrip('./')

                # 使用修正后的路径进行健壮的匹配
                # 1. 匹配目录 (e.g., 'ftl/bkm/bkm_rtbb.c' starts with 'ftl/bkm/')
                if path_to_compare.startswith(normalized_module_path + '/'):
                    return modules['ModuleName'], modules['Owner']
                # 2. 匹配文件 (e.g., 'ftl/bkm.c' == 'ftl/bkm.c')
                if path_to_compare == normalized_module_path:
                    return modules['ModuleName'], modules['Owner']

        return None, None

if __name__ == "__main__":
    import sys
    # 检查是否收到了文件路径参数
    if len(sys.argv) < 2:
        sys.exit()

    # 从命令行第一个参数获取要检查的文件路径
    file_to_check = sys.argv[1]

    # 初始化Farmland类, root_path设为当前工作目录, yml文件在同级
    farmland = Farmland('./', 'farmland.yml')

    # 获取模块名和负责人
    name, owner = farmland.get_info_by_file_path(file_to_check)

    # 如果找到了负责人，就打印“模块名,负责人”
    if owner:
        print(f"{name},{owner}")